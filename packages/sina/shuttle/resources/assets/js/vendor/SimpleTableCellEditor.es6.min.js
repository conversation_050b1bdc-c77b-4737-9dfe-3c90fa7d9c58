"use strict";function _classCallCheck(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function");}var _createClass=function(){function n(n,t){for(var i,r=0;r<t.length;r++)i=t[r],i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}return function(t,i,r){return i&&n(t.prototype,i),r&&n(t,r),t}}(),SimpleTableCellEdition,SimpleTableCellEditor;window.onload=function(){if(!window.jQuery)throw"jQuery is not loaded";};SimpleTableCellEdition=function SimpleTableCellEdition(n,t){_classCallCheck(this,SimpleTableCellEdition);this.Elem=n;this.oldContent=$(n).html();this.oldValue=t.internals.extractValue(n);this.cellParams=t};SimpleTableCellEditor=function(){function n(t,i){_classCallCheck(this,n);var r=this;r.EditionEndOrigin={OutsideTable:1,AnotherCell:2};typeof t=="undefined"&&(t="table");this.tableId=t;this.params=r._GetExtendedEditorParams(i);this.CellEdition=null;this._TryHandleDataTableReloadEvent();$(document).mouseup(function(n){var t=$("#"+r.tableId);t.is(n.target)||t.has(n.target).length!==0||r._FreeCurrentCell(r.EditionEndOrigin.OutsideTable);return})}return _createClass(n,[{key:"SetEditable",value:function(n,t){var i=this,r;if(i._isValidElem(n)){r=i._GetExtendedCellParams(t);$(n).on("click",function(){$(this).hasClass(i.params.inEditClass)||i._EditCell(this,r)});$(n).on("keydown",function(n){$(this).hasClass(i.params.inEditClass)&&i._HandleKeyPressed(n.which,this,r)})}}},{key:"SetEditableClass",value:function(n,t){var i=this,r=i._GetExtendedCellParams(t);$("#"+this.tableId).on("click","td."+n+":not(."+i.params.inEditClass+")",function(){i._EditCell(this,r)});$("#"+this.tableId).on("keydown","td."+n+"."+i.params.inEditClass,function(n){i._HandleKeyPressed(n.which,this,r)})}},{key:"_HandleKeyPressed",value:function(n,t,i){i.keys.validation.includes(n)?this._FreeCell(t,i,!0):i.keys.cancellation.includes(n)&&this._FreeCell(t,i,!1)}},{key:"_EditCell",value:function(n,t){var r=this._FireOnEditEnterEvent(n),i;r.isDefaultPrevented()||(this._FreeCurrentCell(this.EditionEndOrigin.AnotherCell),this.CellEdition=new SimpleTableCellEdition(n,t),this.isDataTable&&(this.CellEdition.cellIndex=$("#"+this.tableId).DataTable().cell($(n)).index()),i=t.internals.extractValue(n),$(n).addClass(this.params.inEditClass),t.internals.renderEditor(n,i),this._FireOnEditEnteredEvent(n,i))}},{key:"_EndEditCell",value:function(n,t){this._FreeCell(n,t,!0)}},{key:"_CancelEditCell",value:function(n,t){this._FreeCell(n,t,!1)}},{key:"_FreeCell",value:function(n,t,i){var f,r,u;this._isValidElem(n)&&this.CellEdition!==null&&((f=this._FireOnEditExitEvent(n,this.CellEdition.oldValue),f.isDefaultPrevented())||(r=t.internals.extractEditorValue(n),$(n).removeClass(this.params.inEditClass),$(n).html(""),t.validation(r)&&this.CellEdition.oldValue!==r||(i=!1),u=t.formatter(r),this._FireOnEditExitedEvent(n,this.CellEdition.oldValue,u,i),i?(t.internals.renderValue(n,u),this._FireEditedEvent(n,this.CellEdition.oldValue,u)):$(n).html(this.CellEdition.oldContent),this.CellEdition=null))}},{key:"_FreeCurrentCell",value:function(n){var t=this._GetCurrentEdition(),i;t!==null&&(i=!0,n===this.EditionEndOrigin.OutsideTable&&t.cellParams.behaviour.outsideTableClickCauseCancellation&&(i=!1),n===this.EditionEndOrigin.AnotherCell&&t.cellParams.behaviour.anotherCellClickCauseCancellation&&(i=!1),this._FreeCell(t.Elem,t.cellParams,i))}},{key:"_GetCurrentEdition",value:function(){return this.CellEdition===null?null:this.CellEdition}},{key:"_GetExtendedEditorParams",value:function(n){var t=this;return $.extend(!0,{},t._GetDefaultEditorParams(),n)}},{key:"_GetExtendedCellParams",value:function(n){var t=this;return $.extend(!0,{},t._GetDefaultCellParams(),n)}},{key:"_FireOnEditEnterEvent",value:function(n){var t=jQuery.Event("cell:onEditEnter",{element:n});return $("#"+this.tableId).trigger(t),t}},{key:"_FireOnEditEnteredEvent",value:function(n,t){$("#"+this.tableId).trigger({type:"cell:onEditEntered",element:n,oldValue:t})}},{key:"_FireOnEditExitEvent",value:function(n,t){var i=jQuery.Event("cell:onEditExit",{element:n,oldValue:t});return $("#"+this.tableId).trigger(i),i}},{key:"_FireOnEditExitedEvent",value:function(n,t,i,r){$("#"+this.tableId).trigger({type:"cell:onEditExited",element:n,newValue:i,oldValue:t,applied:r})}},{key:"_FireEditedEvent",value:function(n,t,i){$("#"+this.tableId).trigger({type:"cell:edited",element:n,newValue:i,oldValue:t})}},{key:"_TryHandleDataTableReloadEvent",value:function(){var n=this;this.isDataTable=!1;try{$.fn.DataTable.isDataTable("#"+n.tableId)&&(n.isDataTable=!0)}catch(t){return}if(n.isDataTable)$("#"+n.tableId).on("draw.dt",function(){if(n.CellEdition!==null&&n.CellEdition.Elem!==null){var t=$("#"+n.tableId).DataTable().cell(n.CellEdition.cellIndex).node();n._EditCell(t,n.CellEdition.cellParams)}})}},{key:"_GetDefaultEditorParams",value:function(){return{inEditClass:"inEdit"}}},{key:"_GetDefaultCellParams",value:function(){return{validation:function(){return!0},formatter:function(n){return n},keys:{validation:[13],cancellation:[27]},behaviour:{outsideTableClickCauseCancellation:!1,anotherCellClickCauseCancellation:!1},internals:this._GetDefaultInternals()}}},{key:"_GetDefaultInternals",value:function(){return{renderValue:function(n,t){$(n).text(t)},renderEditor:function(n,t){$(n).html("<input type='text' style=\"width:100%; max-width:none\">");var i=$(n).find("input");i.focus();i.val(t)},extractEditorValue:function(n){return $(n).find("input").val()},extractValue:function(n){return $(n).text()}}}},{key:"_isValidElem",value:function(n){return n!==null&&typeof n!="undefined"&&$(n).length>0}}]),n}();