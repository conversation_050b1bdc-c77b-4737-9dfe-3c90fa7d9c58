{"v": "5.5.7", "fr": 29.9700012207031, "ip": 0, "op": 100.000004073084, "w": 1000, "h": 1000, "nm": "Business_Analysis", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Control", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [500, 500, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.15, 0.15, 0.15], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 74, "s": [100, 100, 100]}, {"t": 90.0000036657751, "s": [100, 100, 100]}]}}, "ao": 0, "ef": [{"ty": 5, "nm": "Stroke Width", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 12}}]}, {"ty": 5, "nm": "Color 1", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.678431391716, 0.596078455448, 0.960784316063, 1]}}]}, {"ty": 5, "nm": "Color 2", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.854901969433, 0.811764717102, 1, 1]}}]}, {"ty": 5, "nm": "Color 3", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.349019616842, 0.854901969433, 0.643137276173, 1]}}]}, {"ty": 5, "nm": "Color 4", "np": 3, "mn": "ADBE Color Control", "ix": 5, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 0, 0, 1]}}]}, {"ty": 5, "nm": "Color 5", "np": 3, "mn": "ADBE Color Control", "ix": 6, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1, 1]}}]}, {"ty": 5, "nm": "Stroke Color", "np": 3, "mn": "ADBE Color Control", "ix": 7, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.070419065654, 0.276225864887, 0.945098042488, 1]}}]}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": false, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Merged Shape Layer 1", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [439.859, -93.912, 0]}, "a": {"a": 0, "k": [248.328, 685.364, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.15, 0.15, 0.15], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 12, "s": [0, 0, 100]}, {"t": 54.0000021994651, "s": [90, 90, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[251.829, 512.792], [251.829, 537.986]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[251.829, 576.218], [251.829, 601.412]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[207.518, 557.102], [232.712, 557.102]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[270.945, 557.102], [296.139, 557.102]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [248.329, 653.651]}, "a": {"a": 0, "k": [251.829, 525.389]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Partners - 15", "bm": 0, "hd": false}], "ip": 12.00000048877, "op": 912.000037146522, "st": 12.00000048877, "cp": true, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Merged Sha<PERSON>er 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-401.672, 230.364, 0]}, "a": {"a": 0, "k": [248.328, 685.364, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.15, 0.15, 0.15], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 8, "s": [100, 100, 100]}, {"t": 50.0000020365418, "s": [117.647, 117.647, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[251.829, 512.792], [251.829, 537.986]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[251.829, 576.218], [251.829, 601.412]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[207.518, 557.102], [232.712, 557.102]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[270.945, 557.102], [296.139, 557.102]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [248.329, 653.651]}, "a": {"a": 0, "k": [251.829, 525.389]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Partners - 15", "bm": 0, "hd": false}], "ip": 8.00000032584668, "op": 908.000036983598, "st": 8.00000032584668, "cp": true, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Analysis - 1", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [482.784, 707.734, 0]}, "a": {"a": 0, "k": [482.784, 707.734, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[651.43, 707.734], [314.138, 707.734]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [0]}, {"t": 35.0000014255792, "s": [100]}]}, "e": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 15.0000006109625, "op": 915.000037268714, "st": 15.0000006109625, "cp": true, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Analysis - 2", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [196.915, 707.734, 0]}, "a": {"a": 0, "k": [196.915, 707.734, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[246.774, 707.734], [147.057, 707.734]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [0]}, {"t": 35.0000014255792, "s": [100]}]}, "e": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 21.0000008553475, "op": 921.000037513099, "st": 21.0000008553475, "cp": true, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Analysis - 3", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [36.78, 202.983, 0]}, "a": {"a": 0, "k": [36.78, 202.983, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[36.78, 166.315], [36.78, 239.65]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [100]}, {"t": 30.0000012219251, "s": [0]}]}, "e": {"a": 0, "k": 100}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}], "ip": 10.0000004073083, "op": 910.00003706506, "st": 10.0000004073083, "cp": true, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Analysis - 4", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [36.78, 387.269, 0]}, "a": {"a": 0, "k": [36.78, 387.269, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[36.78, 267.763], [36.78, 506.774]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [100]}, {"t": 34.0000013848484, "s": [0]}]}, "e": {"a": 0, "k": 100}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}], "ip": 14.0000005702317, "op": 914.000037227983, "st": 14.0000005702317, "cp": true, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Analysis - 5", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [681.241, 499.677, 0]}, "a": {"a": 0, "k": [681.241, 499.677, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[166.351, 82.63], [-166.351, 82.63], [-166.351, -82.63]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [681.241, 499.677]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 23.0000009368092, "s": [0]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Analysis - 6", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [786.888, 489.361, 0]}, "a": {"a": 0, "k": [786.888, 489.361, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-35.742, 91.45], [-35.742, -91.45], [35.742, -91.45], [35.742, 91.45]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [786.888, 489.361]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [0]}, {"t": 29.0000011811942, "s": [100]}]}, "e": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}], "ip": 9.00000036657752, "op": 909.000037024329, "st": 9.00000036657752, "cp": true, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Analysis - 7", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [786.888, 580.81, 0]}, "a": {"a": 0, "k": [786.888, 580.81, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 9, "s": [100, 0, 100]}, {"t": 29.0000011811942, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-35.742, -91.45], [35.742, -91.45], [35.742, 91.45], [-35.742, 91.45]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078431372549, 0.8352941176470589, 0.00392156862745098, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [786.888, 489.36]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}], "ip": 9.00000036657752, "op": 909.000037024329, "st": 9.00000036657752, "cp": true, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Analysis - 8", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [687.172, 524.504, 0]}, "a": {"a": 0, "k": [687.172, 524.504, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-35.742, 56.307], [-35.742, -56.307], [35.742, -56.307], [35.742, 56.307]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [687.172, 524.504]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"t": 26.0000010590017, "s": [100]}]}, "e": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}], "ip": 6.00000024438501, "op": 906.000036902136, "st": 6.00000024438501, "cp": true, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Analysis - 9", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [687.171, 580.81, 0]}, "a": {"a": 0, "k": [687.171, 580.81, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 6, "s": [100, 0, 100]}, {"t": 26.0000010590017, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-35.742, -56.307], [35.742, -56.307], [35.742, 56.307], [-35.742, 56.307]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078431372549, 0.8352941176470589, 0.00392156862745098, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [687.171, 524.503]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}], "ip": 6.00000024438501, "op": 906.000036902136, "st": 6.00000024438501, "cp": true, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Analysis - 10", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [587.454, 543.793, 0]}, "a": {"a": 0, "k": [587.454, 543.793, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-35.742, 37.018], [-35.742, -37.018], [35.742, -37.018], [35.742, 37.018]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [587.454, 543.793]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [0]}, {"t": 23.0000009368092, "s": [100]}]}, "e": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}], "ip": 3.00000012219251, "op": 903.000036779944, "st": 3.00000012219251, "cp": true, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Analysis - 11", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [587.454, 580.811, 0]}, "a": {"a": 0, "k": [587.454, 580.811, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 3, "s": [100, 0, 100]}, {"t": 23.0000009368092, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-35.742, -37.018], [35.742, -37.018], [35.742, 37.018], [-35.742, 37.018]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078431372549, 0.8352941176470589, 0.00392156862745098, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [587.454, 543.793]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 19", "bm": 0, "hd": false}], "ip": 3.00000012219251, "op": 903.000036779944, "st": 3.00000012219251, "cp": true, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Analysis - 12", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [622.207, 312.276, 0]}, "a": {"a": 0, "k": [622.207, 312.276, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[97.285, 15.022], [-97.285, 15.022], [-97.285, -15.022], [97.285, -15.022]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [100]}, {"t": 29.0000011811942, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [-260]}, {"t": 29.0000011811942, "s": [-340]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [622.207, 312.276]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 20", "bm": 0, "hd": false}], "ip": 6.00000024438501, "op": 906.000036902136, "st": 6.00000024438501, "cp": true, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Analysis - 13", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [676.757, 249.025, 0]}, "a": {"a": 0, "k": [676.757, 249.025, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[151.836, 15.022], [-151.836, 15.022], [-151.836, -15.022], [151.836, -15.022]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [100]}, {"t": 26.0000010590017, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [-260]}, {"t": 26.0000010590017, "s": [-340]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [676.757, 249.025]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 21", "bm": 0, "hd": false}], "ip": 3.00000012219251, "op": 903.000036779944, "st": 3.00000012219251, "cp": true, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Analysis - 14", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [676.757, 185.774, 0]}, "a": {"a": 0, "k": [676.757, 185.774, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[151.836, 15.022], [-151.836, 15.022], [-151.836, -15.022], [151.836, -15.022]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 23.0000009368092, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-260]}, {"t": 23.0000009368092, "s": [-340]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [676.757, 185.774]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 22", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Analysis - 27", "parent": 19, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [382.011, 263.256, 0]}, "a": {"a": 0, "k": [382.011, 263.256, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -71.03], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [71.03, 0]], "v": [[64.305, 64.305], [-64.305, 64.305], [-64.305, -64.305]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [100]}, {"t": 26.0000010590017, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [-260]}, {"t": 26.0000010590017, "s": [-340]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [382.011, 263.256]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 23", "bm": 0, "hd": false}], "ip": 3.00000012219251, "op": 903.000036779944, "st": 3.00000012219251, "cp": true, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Analysis - 15", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [317.706, 327.561, 0]}, "a": {"a": 0, "k": [317.706, 327.561, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 3, "s": [100, 0, 100]}, {"t": 23.0000009368092, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -71.03], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [71.03, 0]], "v": [[64.305, 64.305], [-64.305, 64.305], [-64.305, -64.305]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.996078431372549, 0.8352941176470589, 0.00392156862745098, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [382.011, 263.256]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 23", "bm": 0, "hd": false}], "ip": 3.00000012219251, "op": 903.000036779944, "st": 3.00000012219251, "cp": true, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Analysis - 26", "parent": 21, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [275.539, 366.039, 0]}, "a": {"a": 0, "k": [275.539, 366.039, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [71.031, 0], [0, 71.031], [-71.031, 0], [0, 0]], "o": [[0, 71.031], [-71.031, 0], [0, -71.03], [0, 0], [0, 0]], "v": [[128.61, 0], [0, 128.61], [-128.61, 0], [0, -128.61], [0, 0]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 23.0000009368092, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-260]}, {"t": 23.0000009368092, "s": [-340]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [275.539, 366.039]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 24", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Analysis - 16", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [275.539, 366.039, 0]}, "a": {"a": 0, "k": [275.539, 366.039, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 0, 100]}, {"t": 20.0000008146167, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [71.031, 0], [0, 71.031], [-71.031, 0], [0, 0]], "o": [[0, 71.031], [-71.031, 0], [0, -71.03], [0, 0], [0, 0]], "v": [[128.61, 0], [0, 128.61], [-128.61, 0], [0, -128.61], [0, 0]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.811999990426, 0.2, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [275.539, 366.039]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 24", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "Analysis - 17", "parent": 30, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [484.348, 54.55, 0]}, "a": {"a": 0, "k": [484.348, 54.55, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[455.097, 25.301], [-455.098, 25.301], [-455.098, -25.301], [455.097, -25.301]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.484]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 29.0000011811942, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-270]}, {"t": 26.0000010590017, "s": [-340]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [484.348, 54.55]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 25", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Analysis - 18", "parent": 24, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [484.347, 657.003, 0]}, "a": {"a": 0, "k": [484.347, 657.003, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-362.183, -25.301], [455.097, -25.301], [455.097, 25.3], [-455.097, 25.3], [-455.097, -25.301], [-422.268, -25.301]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.555]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 25.0000010182709, "s": [0]}]}, "o": {"a": 1, "k": [{"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-145]}, {"t": 25.0000010182709, "s": [0]}]}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [484.347, 657.003]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 26", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Analysis - 19", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [484.347, 657.003, 0]}, "a": {"a": 0, "k": [484.347, 657.003, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 20.0000008146167, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-362.183, -25.301], [455.097, -25.301], [455.097, 25.3], [-455.097, 25.3], [-455.097, -25.301], [-422.268, -25.301]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [484.347, 657.003]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 27", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Analysis - 20", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [63.403, 323.767, 0]}, "a": {"a": 0, "k": [63.403, 323.767, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[63.403, 566.102], [63.403, 81.433]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 23.0000009368092, "s": [0]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 28", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Analysis - 21", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [905.293, 159.684, 0]}, "a": {"a": 0, "k": [905.293, 159.684, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[905.293, 80.905], [905.293, 238.462]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 23.0000009368092, "s": [0]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 29", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Analysis - 22", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [905.293, 469.45, 0]}, "a": {"a": 0, "k": [905.293, 469.45, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[905.293, 308.779], [905.293, 630.121]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 11.7, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Control').effect('Stroke Width')('ADBE Slider Control-0001');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100}, "e": {"a": 1, "k": [{"i": {"x": [0.249], "y": [0.591]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 23.0000009368092, "s": [0]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 30", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "<PERSON><PERSON>", "parent": 31, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [483.898, 355.776, 0]}, "a": {"a": 0, "k": [483.898, 355.776, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[420.944, 275.925], [-420.944, 275.925], [-420.944, -275.925], [420.944, -275.925]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [483.898, 355.776]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 32", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Analysis - 23", "parent": 31, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.15, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [484.347, 13.127, 0], "to": [0, 14, 0], "ti": [0, -14, 0]}, {"t": 20.0000008146167, "s": [484.347, 97.127, 0]}]}, "a": {"a": 0, "k": [484.347, 97.127, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[420.945, 17.276], [-420.945, 17.276], [-420.945, -17.276], [420.945, -17.276]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [484.347, 97.127]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 31", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "Analysis - 25", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [484.348, 54.55, 0]}, "a": {"a": 0, "k": [484.348, 54.55, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 20.0000008146167, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[455.097, 25.301], [-455.098, 25.301], [-455.098, -25.301], [455.097, -25.301]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [484.348, 54.55]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 33", "bm": 0, "hd": false}], "ip": 0, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "Analysis - 24", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-8.702, -12.481, 0]}, "a": {"a": 0, "k": [483.898, 355.776, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 20.0000008146167, "s": [100, 100, 100]}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[420.944, 275.925], [-420.944, 275.925], [-420.944, -275.925], [420.944, -275.925]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [483.898, 355.776]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 32", "bm": 0, "hd": false}], "ip": 900.000036657751, "op": 900.000036657751, "st": 0, "cp": true, "bm": 0, "hidden": 0}], "markers": [{"tm": 0, "cm": "1", "dr": 0}, {"tm": 20.0000008146167, "cm": "2", "dr": 0}]}