"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[951],{5951:(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});const r={props:{column:{type:Object,required:!0},value:{type:Object,default:null}},computed:{dbTypes:function(){return window.dbTypes}},methods:{onTypeChange:function(e){this.$emit("input",this.getDbType(e.target.value))},getDbType:function(e){var t;for(var n in e=e.toLowerCase().trim(),this.dbTypes)if(t=this.dbTypes[n].find((function(t){return e==t.name.toLowerCase()})))return t;return databaseTypes.Numbers[0]},getType:function(e){return getDbType(e)}}};const o=(0,n(1900).Z)(r,(function(){var e=this,t=e._self._c;return t("select",{staticClass:"form-control",domProps:{value:e.column.type.name},on:{change:e.onTypeChange}},e._l(e.dbTypes,(function(n,r){return t("optgroup",{key:r,attrs:{label:r}},e._l(n,(function(n){return t("option",{key:n.name,domProps:{value:n.name}},[e._v("\n      "+e._s(n.name.toUpperCase())+"\n    ")])})),0)})),0)}),[],!1,null,null,null).exports}}]);