<?php

namespace Sina\Shuttle\FormFields;

class ImageHandler extends <PERSON>bstract<PERSON>and<PERSON>
{
    protected $codename = 'image';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.image', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
