"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[846],{7846:(t,e,a)=>{a.r(e),a.d(e,{default:()=>u});function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function l(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(Object(a),!0).forEach((function(e){o(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function o(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}const c={props:{value:{type:Object,required:!0}},data:function(){var t;return{columnData:l(l({},null!==(t=this.value)&&void 0!==t?t:{}),{},{defaultType:""})}},computed:{showDefaultInput:function(){return"DEFINED"==this.columnData.defaultType||this.columnData.default}},watch:{columnData:{handler:function(t){this.$emit("input",t)},deep:!0}},mounted:function(){"YES"==this.columnData.null&&null==this.columnData.default?(this.columnData.notnull=!1,this.columnData.defaultType="NULL"):this.columnData.default?this.columnData.defaultType="DEFINED":this.columnData.defaultType=""},methods:{deleteColumn:function(){this.$emit("columnDeleted",this.columnData)},changeDefault:function(){"NULL"==this.columnData.defaultType?(this.columnData.notnull=!1,this.columnData.default=null):""==this.columnData.defaultType&&(this.columnData.default=null)}}};const u=(0,a(1900).Z)(c,(function(){var t=this,e=t._self._c;return e("tr",[e("td",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.columnData.name,expression:"columnData.name"}],staticClass:"form-control",attrs:{type:"text",required:""},domProps:{value:t.columnData.name},on:{input:function(e){e.target.composing||t.$set(t.columnData,"name",e.target.value)}}})]),t._v(" "),e("td",[e("database-type",{attrs:{column:t.columnData},model:{value:t.columnData.type,callback:function(e){t.$set(t.columnData,"type",e)},expression:"columnData.type"}})],1),t._v(" "),e("td",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.columnData.length,expression:"columnData.length"}],staticClass:"form-control",domProps:{value:t.columnData.length},on:{input:function(e){e.target.composing||t.$set(t.columnData,"length",e.target.value)}}})]),t._v(" "),e("td",{staticClass:"text-center"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.columnData.defaultType,expression:"columnData.defaultType"}],staticClass:"form-control",on:{change:[function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){return"_value"in t?t._value:t.value}));t.$set(t.columnData,"defaultType",e.target.multiple?a:a[0])},t.changeDefault]}},[e("option",{attrs:{value:""}},[t._v("None")]),t._v(" "),e("option",{attrs:{value:"NULL"}},[t._v("Null")]),t._v(" "),e("option",{attrs:{value:"DEFINED"}},[t._v("As defined:")])]),t._v(" "),t.showDefaultInput?e("input",{directives:[{name:"model",rawName:"v-model",value:t.columnData.default,expression:"columnData.default"}],staticClass:"form-control mt-1",domProps:{value:t.columnData.default},on:{input:function(e){e.target.composing||t.$set(t.columnData,"default",e.target.value)}}}):t._e()]),t._v(" "),e("td",{staticClass:"text-center"},[e("div",{staticClass:"form-check"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.columnData.unsigned,expression:"columnData.unsigned"}],staticClass:"form-check-input position-static",attrs:{type:"checkbox",value:"option1"},domProps:{checked:Array.isArray(t.columnData.unsigned)?t._i(t.columnData.unsigned,"option1")>-1:t.columnData.unsigned},on:{change:function(e){var a=t.columnData.unsigned,n=e.target,l=!!n.checked;if(Array.isArray(a)){var o="option1",c=t._i(a,o);n.checked?c<0&&t.$set(t.columnData,"unsigned",a.concat([o])):c>-1&&t.$set(t.columnData,"unsigned",a.slice(0,c).concat(a.slice(c+1)))}else t.$set(t.columnData,"unsigned",l)}}})])]),t._v(" "),e("td",{staticClass:"text-center"},[e("div",{staticClass:"form-check"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.columnData.notnull,expression:"columnData.notnull"}],staticClass:"form-check-input position-static",attrs:{type:"checkbox",disabled:"NULL"==t.columnData.defaultType},domProps:{checked:Array.isArray(t.columnData.notnull)?t._i(t.columnData.notnull,null)>-1:t.columnData.notnull},on:{change:function(e){var a=t.columnData.notnull,n=e.target,l=!!n.checked;if(Array.isArray(a)){var o=t._i(a,null);n.checked?o<0&&t.$set(t.columnData,"notnull",a.concat([null])):o>-1&&t.$set(t.columnData,"notnull",a.slice(0,o).concat(a.slice(o+1)))}else t.$set(t.columnData,"notnull",l)}}})])]),t._v(" "),e("td",{staticClass:"text-center"},[e("div",{staticClass:"form-check"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.columnData.autoincrement,expression:"columnData.autoincrement"}],staticClass:"form-check-input position-static",attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.columnData.autoincrement)?t._i(t.columnData.autoincrement,null)>-1:t.columnData.autoincrement},on:{change:function(e){var a=t.columnData.autoincrement,n=e.target,l=!!n.checked;if(Array.isArray(a)){var o=t._i(a,null);n.checked?o<0&&t.$set(t.columnData,"autoincrement",a.concat([null])):o>-1&&t.$set(t.columnData,"autoincrement",a.slice(0,o).concat(a.slice(o+1)))}else t.$set(t.columnData,"autoincrement",l)}}})])]),t._v(" "),e("td",[e("button",{staticClass:"btn btn-bootstrap-padding btn-danger",attrs:{type:"button"},on:{click:function(e){return e.preventDefault(),t.deleteColumn.apply(null,arguments)}}},[e("i",{staticClass:"simple-icon-trash"})])])])}),[],!1,null,null,null).exports}}]);