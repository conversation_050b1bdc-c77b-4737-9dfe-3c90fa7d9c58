@extends('shuttle::admin')
@section('breadcrumbs')
    @include('shuttle::includes.breadcrumbs', ['breadCrumbs' => ['Home' => route('shuttle.index'), 'Types' => route('shuttle.developer.type.index')], 'btn' => route('shuttle.developer.type.create')])
@stop
@section('main')
    <div class="content-body">
        <div class="row" id="table-responsive">
            <div class="col-12">
                <div class="card">
                    <div class="card-content">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Model</th>
                                        <th>Create</th>
                                        <th>Update</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($types as $type)
                                        <tr>
                                            <td>{{$loop->iteration}}</td>
                                            <td>{{$type->name}}</td>
                                            <td>{{$type->model}}</td>
                                            <td>{{$type->created_at}}</td>
                                            <td>{{$type->updated_at}}</td>
                                            <td>
                                                <a href="{{route('shuttle.developer.type.edit',$type)}}" class="btn btn-primary default">Edit</a>
                                                <button type="button" class="btn btn-danger default">Delete</button>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
