<?php

namespace App\View\Shuttle;

use App\Models\CourseLocation;
use Sina\Shuttle\View\ShuttleComponent;

class Packages extends ShuttleComponent
{
    public function additional() : array
    {
        return [
            'courses' => CourseLocation::whereIn('course_id', function ($q){
                $q->select('id')->from('courses');
            })->with('course.category', 'location')->orderBy('start_at', 'asc')->limit(6)->get()
        ];
    }
}
