<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AwwardPackage extends Model
{
    use HasFactory;

    protected $guarded = ['id'];
    protected $table = 'awwards_packages';

    public $casts = [
        
        'start_at' => 'date',
        'end_at' => 'date',
    ];
    public $timestamps = false;

    public function awward(){
        return $this->belongsTo(Awward::class);
    }

    public function location(){
        return $this->belongsTo(Location::class);
    }

    public function getDurationAttribute(){
        return ($this->start_at->diffInDays($this->end_at) ?? 0) + 1;
    }
    public function getUpComingFormatAttribute(){
        if($this->start_at->year != $this->end_at->year){
            return $this->start_at->format('d M Y').' - '.$this->end_at->format('d M Y');
        }

        if($this->start_at->month != $this->end_at->month){
            return $this->start_at->format('d M').' - '.$this->end_at->format('d M').', '.$this->start_at->year;
        }


        return $this->start_at->format('d').' - '.$this->end_at->format('d').' '.$this->start_at->format('M').', '.$this->start_at->year;
    }
}
