@font-face {
    font-family: "MarkGEO-Regular";
    src: url("../fonts/MarkGEO-Regular.woff2");
}

@font-face {
    font-family: "MarkGEO-Bold";
    src: url("../fonts/MarkGEO-Bold.woff2");
}

@font-face {
    font-family: "Firago-Bold";
    src: url("../fonts/FiraGO-SemiBoldf.woff");
}


.remove-it {
    width: 30px;
    height: 30px;
    background: red;
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    outline: none;
    margin-bottom: 10px;
    border-radius: 10px;
    position: absolute;
    right: 20px;
    top: 10px;
    z-index: 9999;
}

.remove-it svg {
    width: 15px;
    height: 15px;
}

.remove-it svg path {
    stroke:#fff;
}

#accordion .card-header button {
    border: none;
    outline: none;
    background: none;
    font-size: 18px;
    color: #fff;
    font-weight: bold;
    width: 100%;
    cursor: pointer;
    text-align: left;
    float: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#accordion .card-header button svg {
    width: 24px;
    height: 24px;
}

#accordion .card-header button svg path {
    fill:#fff;
}

.cm {
    width: 100%;
    float:left;
    margin-top: 40px;
}

.cm-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    float:left;
    margin-bottom: 30px;
    color:#fff;
}

.cm-content {
    display: flex;
    align-items: center;
}

.cm-content button {
    margin-left: 20px;
}

.cm-area {
    border:2px solid rgba(255,255,255,.1);
    padding:30px;
    border-radius: 15px;
    float: left;
    width: 100%;
    margin-top: 30px;
}

.choose .custom-control {
    display: flex;
    align-items: center;
}

.choose {
    margin-bottom: 40px;
}

.media-thumb-container {
    margin-top:0!important;
}

html[data-theme=light] #accordion .card-header button {
    color:#242424;
}

html[data-theme=light] #accordion .card-header button svg path{
    fill:#242424;
}

.select2-container--bootstrap .select2-selection {
    background: none;
}


/*.page-content__item , .page-title {*/
/*    flex-direction: unset;*/
/*    display: flex;*/
/*    overflow-y: hidden;*/
/*    overflow-x: scroll;*/
/*    white-space: nowrap;*/
/*}*/


/*.page-content__item::-webkit-scrollbar {*/
/*    width: 5px;*/
/*    height: 5px;*/
/*}*/

/*.page-content__item::-webkit-scrollbar-track {*/
/*    background: #fff*/
/*}*/

/*.page-content__item::-webkit-scrollbar-thumb {*/
/*    background: #d02333*/
/*}*/

/*.page-content__item .item-init {*/
/*    word-break: break-all;*/
/*    white-space: nowrap;*/
/*    padding:0 10px;*/
/*}*/


.modal {
    z-index: 9999999
}

.modal-backdrop {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background: rgba(1,0,0,.7);
    opacity: 1!important
}

.modal .modal-dialog {
    border: none;
    border-radius: 20px;
    max-width: 1236px!important;
    overflow: hidden;
    padding: 0
}

.modal-content {
    background: #2a2e3f;
    border-radius: 20px;
    overflow: hidden
}

.modal .smallbody {
    padding: 40px 30px
}

.modal .modal-header {
    border: none;
    display: flex;
    padding-left: 30px;
    padding-top: 40px
}

.modal .modal-header h5 {
    color: #fff;
    float: left;
    font-size: 19px;
    padding-right: 30px;
    text-align: left;
    width: 100%
}

.modal .close {
    align-items: center;
    background: #38cab3;
    border-radius: 10px;
    color: #fff;
    display: flex;
    font-size: 26px;
    height: 50px;
    justify-content: center;
    opacity: 1;
    position: absolute;
    right: 40px;
    text-shadow: none;
    top: 40px;
    width: 50px
}

.modal-body {
    padding: 30px 30px 0
}

.modal-body .btn-add {
    font-size: 13px;
    padding: 0 20px!important;
    width: auto!important
}

.modal-body .btn-add svg {
    margin-right: 10px
}

.modal-body .btn-add svg path {
    stroke: none;
    fill: #fff
}



.l {
    width: 100%;
    float: left;
    margin-bottom: 30px
}

.l-item {
    position: relative;
    background: #fff;
    -webkit-box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    padding: 30px;
    border-radius: 15px;
    overflow: hidden
}

.l-item__title {
    width: 70%;
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.l-item__title span {
    color: #d02333;
    margin-bottom: 10px
}

.l-item__title h2 a {
    color: #242424;
    font-weight: bolder;
    line-height: 1.5;
}

.filter-block__title h1 {
    font-family: "p_regular";
}



.l-item__title h2 a:hover {
    color: #d02333
}

.l-item__title ul {
    margin-top: 15px
}

.l-item__title ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px
}

.l-item__title ul li:last-child {
    margin-bottom: 0
}

.l-item__title ul li figure {
    width: 50px;
    height: 50px;
    border: 1px solid #f2f2f2;
    border-radius: 10px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.l-item__title ul li figure svg {
    width: 25px;
    height: 25px
}

.l-item__title ul li p {

    margin-bottom: 5px;
    color: #888
}

.l-item__title a {
    margin-top: 20px
}

.l-item__title a:hover {
    color: #2f4858
}

.l-item__img {
    width: 30%;
    float: left;
    height: 100%;
    position: absolute;
    right: 0;
    overflow: hidden;
    top: 0
}

.l-item__img figure {
    height: 100%;
    background: #2f4858
}

.l-item__img figure img {
    opacity: .3;
    width:100%;
    height: 100%;
    object-fit: cover;
}

.l-item__img span {
    position: absolute;
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    color: #fff;
    top: 50px;
    right: -10px;

    font-size: 18px;
    letter-spacing: 2px
}

.l-item__img .price {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: #fff;
    font-size: 24px;
    color: #fff
}


.btn-reg {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #d02333;
    border-radius: 15px;
    color: #fff;
    padding:15px 30px!important;
    transition: 0.3s all ease-in;
}

.btn-reg:hover {
    background: #2f4858;
    color:#fff!important;
}

.btn-reg svg {
    width: 21px;
    height: 21px;
    margin-right: 15px
}

.btn-reg svg path {
    stroke: #fff
}


.section-title h1 {
    font-weight: bolder;
}

.section-title {
    width: 100%;
    float:left;
    margin-bottom: 30px;
}

.list {
    width: 100%;
    float: left;
}

.list ul {
    width: 100%;
    float: left;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.list ul li {
    width: 48%;
    float: left;
    margin-bottom: 15px;
    border:1px solid #f2f2f2;
    border-radius: 10px;
    padding:15px 30px;
    font-size: 12px;
}

.list ul li strong {
    font-weight: bold;
    color:var(--c1);
    text-transform: uppercase;
}

.userinfo {
    width: 100%;
    float: left;
}

.name {
    display: flex;
    align-items: center;
    background: #f2f2f2;
    border-radius: 15px;
    margin-bottom: 30px;
    padding:15px;
}


.name figure {
    border:2px solid var(--c1);
    padding:5px;
    border-radius: 50%;
    overflow: hidden;
    width: 80px;
    height: 80px;
    float: left;
    margin-right: 15px;
}

.name figure img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.userinfo  ul li {
    width: 100%;
    float: left;
    margin-bottom: 15px;
    border:1px solid #f2f2f2;
    border-radius: 10px;
    padding:15px 30px;
    font-size: 12px;
    
}

.userinfo ul li strong {
    font-weight: bold;
    color:var(--c1);
    text-transform: uppercase;
}

.infos h2 {
    font-weight: bolder;
    text-transform: uppercase;
    color:var(--c1);
    margin-bottom: 10px;
}