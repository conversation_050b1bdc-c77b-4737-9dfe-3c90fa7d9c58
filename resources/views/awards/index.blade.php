@extends('app')

@section('content')
    @include('includes.header')

    <section class="section courses">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <!-- <div class="section-title">
                        <span>@lang('common.awwards_title_1')</span>
                        <h1>@lang('common.awwards_title_2')</h1>
                        <ul>
                            <li class="sprev"><i class="icofont-rounded-left"></i></li>
                            <li class="snext"><i class="icofont-rounded-right"></i></li>
                        </ul>
                    </div> -->
                    <!-- <div class="courses-slider">
                        <div class="swiper-container courses-slider__slider" id="slider">
                            <div class="swiper-wrapper">
                                @foreach ($categories as $cat)
                                    <div class="swiper-slide">
                                        <div class="courses-slider__item">
                                            <a href="?category_id={{ $cat->id }}">
                                                <figure>
                                                    <img src="{{ Storage::url($cat->image) }}" alt="" />
                                                </figure>
                                                <div class="title-in">
                                                    <h2>{{ $cat->title }}</h2>
                                                    <span>{{ $cat->awward_count }} Awards</span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div> -->
                </div>
                <style>
                    .courses:before {
                        width: 100%;
                        height: 100px !important;
                    }
                    .courses-items {
                        width: calc(100% - 10px) !important;
                    }
                </style>


                <div class="col-md-12 ">
                    <div class="spead">
                        <ul>
                            <li><a href="#"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M18.4384 20C19.3561 20 20.1493 19.3726 20.2725 18.4632C20.3895 17.5988 20.5 16.4098 20.5 15C20.5 12 20.6683 10.1684 17.5 7C16.0386 5.53865 14.4064 4.41899 13.3024 3.74088C12.4978 3.24665 11.5021 3.24665 10.6975 3.74088C9.5935 4.41899 7.96131 5.53865 6.49996 7C3.33157 10.1684 3.49997 12 3.49997 15C3.49997 16.4098 3.61039 17.5988 3.72745 18.4631C3.85061 19.3726 4.64378 20 5.56152 20H18.4384Z"
                                            stroke="#000000" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg> @lang('common.home')</a></li>
                            <li class="active">@lang('common.awwards')</li>
                        </ul>
                    </div>


                    <div class="courses-items">
                        <div class="courses-items__list">
                            <div class="row">
                                @include('includes.awward-list', ['awward' => $awward])
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </section>

    <section class="section partners partners-full">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <div class="icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1284.000000pt" height="1058.000000pt"
                                viewBox="0 0 1284.000000 1058.000000" preserveAspectRatio="xMidYMid meet">
                                <g transform="translate(0.000000,1058.000000) scale(0.100000,-0.100000)" fill="#000000"
                                    stroke="none">
                                    <path d="M5409 8514 c-80 -71 -174 -154 -210 -185 -35 -31 -224 -197 -419
                               -369 -195 -172 -440 -388 -544 -479 -104 -91 -196 -173 -205 -181 -9 -8 -144
                               -127 -301 -265 -157 -137 -328 -287 -380 -334 -52 -46 -108 -96 -125 -110 -16
                               -15 -187 -165 -380 -335 -192 -170 -399 -352 -460 -404 -226 -197 -277 -265
                               -311 -419 -20 -92 -14 -189 17 -281 l21 -63 27 24 c14 13 37 34 51 46 82 74
                               203 140 327 177 78 24 105 27 238 28 223 1 268 -15 570 -198 502 -304 578
                               -348 621 -363 62 -21 164 -13 224 17 25 13 122 90 215 173 94 82 313 275 487
                               428 174 153 345 311 379 351 115 134 199 284 253 458 41 128 72 368 47 358 -4
                               -1 -39 -31 -77 -64 -38 -34 -172 -153 -299 -264 -126 -111 -355 -313 -508
                               -449 -154 -135 -300 -259 -327 -274 -103 -60 -225 -43 -312 44 -29 29 -48 56
                               -46 67 3 14 224 215 503 455 33 29 85 75 115 102 30 28 89 80 130 115 41 36
                               77 67 80 70 3 3 41 37 85 76 44 39 127 112 185 163 58 51 131 123 164 160 166
                               187 278 435 306 678 21 178 54 194 -366 -176 -205 -179 -405 -356 -445 -392
                               -41 -36 -150 -132 -244 -214 -93 -82 -193 -169 -220 -194 -28 -25 -100 -89
                               -160 -141 -61 -52 -128 -111 -149 -130 -57 -51 -54 -49 -146 -130 -47 -41 -87
                               -77 -90 -80 -32 -32 -37 -32 -130 15 -50 25 -130 56 -177 69 l-86 22 124 110
                               c68 60 205 181 304 269 100 88 286 252 415 365 129 113 259 228 290 255 97 87
                               203 181 261 231 230 199 457 404 504 457 159 174 276 423 310 656 9 60 15 126
                               13 146 l-3 37 -146 -128z" />
                                    <path d="M1563 8149 c13 -196 91 -423 204 -591 72 -107 148 -190 303 -326 385
                               -341 519 -458 624 -551 51 -44 61 -49 75 -38 9 8 57 49 106 92 197 172 211
                               187 186 197 -5 2 -60 48 -122 103 -62 55 -158 140 -213 189 -56 49 -117 103
                               -136 120 -19 17 -163 144 -320 281 -157 137 -290 254 -296 260 -73 67 -399
                               351 -407 354 -7 3 -8 -25 -4 -90z" />
                                    <path d="M1560 7168 c0 -201 92 -467 228 -663 70 -101 117 -151 265 -282 104
                               -93 120 -103 135 -92 9 8 71 61 137 120 66 58 130 114 143 125 12 10 22 21 22
                               25 0 4 -86 82 -192 175 -204 180 -450 397 -508 448 -19 17 -73 65 -120 106
                               -47 41 -86 78 -88 83 -11 23 -22 0 -22 -45z" />

                                </g>
                            </svg>

                        </div>
                        <!-- /.icon -->
                        <h1>@lang('common.partners_title_2')</h1>
                    </div>
                    <!-- /.section-title -->
                </div>
                <!-- /.col-md-12 -->
                <div class="col-md-12">
                    <div class="partners-slider">
                        <div class="partners-slider__carousel swiper-container">
                            <div class="swiper-wrapper">
                                @foreach ($clients as $client)
                                    <div class="swiper-slide">
                                        <div class="partners-item__carousel-item">
                                            <a href="{{ $client->link }}" title="{{ $client->name }}">
                                                <figure>
                                                    <img src="{{ Storage::url($client->image) }}"
                                                        alt="{{ $client->name }}" />
                                                </figure>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.col-md-12 -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container -->
    </section>
@stop
