<?php

namespace Sina\Shuttle\FormFields;

class TimeHandler extends <PERSON>bstract<PERSON>and<PERSON>
{
    protected $codename = 'time';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.time', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
