/* http://meyerweb.com/eric/tools/css/reset/ 
   v2.0 | 20110126
   License: none (public domain)
*/

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}


/* HTML5 display-role reset for older browsers */

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

body {
    line-height: 1;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

%mfont {
    font-family: "MarkGEO-Bold";
}

%regular {
    font-family: "MarkGEO-Regular";
}

%medium {
    font-family: "MarkGEO-Regular", sans-serif;
    ;
}

%noto {
    font-family: "MarkGEO-Regular", sans-serif;
    font-style: normal;
}

%img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

%flex {
    display: flex;
    justify-content: center;
    align-items: center;
}

%sls {
    border: 2px solid transparent;
    padding: 15px 30px;
    cursor: pointer;
    font-size: 14px;
    @extend %noto;
    font-size: 14px;
    display: flex;
    align-items: center;
}

%float {
    width: 100%;
    float: left;
}

%btn {
    padding: 13px 30px;
    font-family: "MarkGEO-Regular";
    font-size: 13px;
}

h1 {
    font-size: 21px;
}

h2 {
    font-size: 18px;
}

h3 {
    font-size: 16px;
}

h4 {
    font-size: 14px;
}

$mcol: #d02333;
$scol: #2a2e3f;
$ccol:#d02333;
$dcol: #c5cfcd;
$ecol: #2D2D2D;
.container {
    max-width: 100%;
    padding: 0;
}

body {
    background: #383d52;
    @extend %regular;
    overflow-x: hidden;
    &::-webkit-scrollbar {
        width: 5px;
    }
    &::-webkit-scrollbar-track {
        background: $scol;
    }
    &::-webkit-scrollbar-thumb {
        background: $mcol;
        border-radius: 5px;
    }
}

main {
    width: calc(100% - 250px);
    float: right;
    padding: 0 30px;
    padding-bottom: 50px;
    padding-top: 80px;
}

a {
    color: $mcol;
    &:hover {
        color: $ccol;
        @extend %mfont;
        text-decoration: none;
    }
}

.footer {
    @extend %float;
    @extend %flex;
    color: #fff;
    font-size: 14px;
    margin-top: 100px;
}

.menu {
    width: 250px;
    float: left;
    background: $scol;
    border-right: 1px solid #484863;
    height: calc(100vh - 80px);
    padding: 30px;
    overflow-y: scroll;
    position: fixed;
    top: 80px;
    &::-webkit-scrollbar {
        width: 5px;
    }
    &::-webkit-scrollbar-track {
        background: $scol;
    }
    &::-webkit-scrollbar-thumb {
        background: $mcol;
        border-radius: 5px;
    }
    h3 {
        @extend %mfont;
        color: $dcol;
        font-size: 14px
    }
    ul {
        @extend %float;
        margin-top: 30px;
        li {
            @extend %float;
            margin-bottom: 20px;
            &:last-child {
                margin-bottom: 0;
            }
            a {
                display: flex;
                align-items: center;
                color: #eaecf3;
                transition: 0.3s all ease-in;
                svg {
                    width: 21px;
                    height: 21px;
                    margin-right: 10px;
                    path {
                        stroke: #bfc8de;
                        transition: 0.3s all ease-in;
                    }
                }
                &:hover {
                    color: $mcol;
                    svg {
                        path {
                            stroke: $mcol;
                        }
                    }
                }
            }
        }
    }
}

.header {
    @extend %float;
    background: $scol;
    border-bottom: 1px solid #484863;
    height: 80px;
    position: fixed;
    top: 0;
    z-index: 900;
    &-top {
        @extend %float;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &__logo {
            width: 250px;
            border-right: 1px solid #484863;
            height: 80px;
            @extend %flex;
            a {
                color: #fff;
                @extend %mfont;
                display: flex;
                align-items: center;
                font-size: 24px;
                text-transform: uppercase;
                span {
                    width: 5px;
                    height: 5px;
                    background: $mcol;
                    float: right;
                    margin-left: 10px;
                    border-radius: 2px;
                }
            }
        }
        &__actions {
            padding-right: 45px;
            .user {
                display: flex;
                align-items: center;
                &-img {
                    margin-right: 20px;
                    figure {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        overflow: hidden;
                        border: 2px solid $mcol;
                        padding: 2px;
                        img {
                            @extend %img;
                            border-radius: 50%;
                        }
                    }
                }
                &-title {
                    display: flex;
                    flex-direction: column;
                    h3 {
                        font-size: 14px;
                        color: #fff;
                        margin-bottom: 5px;
                        float: left;
                    }
                    a {
                        color: $mcol;
                        font-size: 12px;
                    }
                }
            }
        }
    }
}

.btn {
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .4)!important;
    display: inline-block;
    font-weight: 400;
    color: #4a4a69;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.5rem 0.78rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: 5px;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.card {
    background: none;
    box-shadow: none;
    outline: none;
    border: none;
    padding: 0;
    color: $dcol;
}

.page {
    @extend %float;
    &-title {
        background-color: #2a2e3f!important;
        box-shadow: 0 0 10px rgba(28, 39, 60, .1);
        @extend %float;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 0;
        color: $dcol;
        @extend %mfont;
        border-radius: 10px;
        font-size: 14px;
        margin-bottom: 10px;
        &>div {
            width: calc(100% / 6);
            @extend %flex;
        }
    }
    &-content {
        &__item {
            background-color: #2a2e3f!important;
            box-shadow: 0 0 10px rgba(28, 39, 60, .1);
            @extend %float;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            margin-bottom: 5px;
            color: $dcol;
            border-radius: 10px;
            font-size: 14px;
            .item-init {
                width: calc(100% / 6);
                @extend %flex;
                text-align: center;
                line-height: 1.5;
                a {
                    @extend %flex;
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    padding: 0;
                    svg {
                        width: 18px;
                        height: 18px;
                        path {
                            stroke: #fff;
                        }
                        polygon {
                            stroke: #fff;
                        }
                    }
                }
                a.show-page {
                    width: auto;
                }
                button {
                    @extend %flex;
                    margin-left: 15px;
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    padding: 0;
                    svg {
                        width: 18px;
                        height: 18px;
                        path {
                            stroke: rgba(247, 79, 117, 1);
                            ;
                        }
                    }
                }
            }
        }
    }
}

.btn-primary {
    background: $mcol;
    color: #fff;
    transition: 0.3s all ease-in;
    &:Hover {
        background: $ccol;
        border-color: $ccol;
    }
    &:focus {
        background: $ccol;
        border-color: $ccol;
    }
}

.btn-danger {
    background-color: rgba(247, 79, 117, .2);
    color: rgba(247, 79, 117, 1);
    transition: 0.3s all ease-in;
    &:hover {
        background: rgba(247, 79, 117, 1);
        border-color: rgba(247, 79, 117, 1);
        svg {
            path {
                stroke: #fff!important;
            }
        }
    }
    &:focus {
        background: rgba(247, 79, 117, 1);
        border-color: rgba(247, 79, 117, 1);
        svg {
            path {
                stroke: #fff!important;
            }
        }
    }
}

.pageTitle {
    @extend %float;
    padding: 50px 0;
    display: flex;
    flex-direction: column;
    &-title {
        h1 {
            @extend %mfont;
            color: $dcol;
            font-size: 24px;
        }
    }
    &-down {
        @extend %float;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        ul {
            display: flex;
            align-items: center;
            li {
                a {
                    color: $dcol;
                }
                &.active {
                    a {
                        color: $mcol;
                    }
                }
            }
        }
        a.btn {
            display: flex;
            align-items: center;
            svg {
                width: 18px;
                height: 18px;
                margin-right: 10px;
                line {
                    stroke: #fff;
                }
            }
        }
    }
}

.choose-lang {
    @extend %float;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h1 {
        font-size: 16px;
        color: #fff;
        margin-right: 30px;
    }
    ul {
        display: flex;
        align-items: center;
        li {
            float: left;
            margin-right: 20px;
            &:last-child {
                margin-right: 0;
            }
            a {
                padding: 10px 20px;
                border-radius: 10px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                text-transform: uppercase;
                font-size: 14px;
                img {
                    margin-right: 10px;
                }
                background-color: rgba(247, 79, 117, .2);
                color: rgba(247, 79, 117, 1);
                transition: 0.3s all ease-in;
                &:hover {
                    background: rgba(247, 79, 117, 1);
                    border-color: rgba(247, 79, 117, 1);
                    color: #242424;
                }
            }
            &.active {
                a {
                    background: rgba(247, 79, 117, 1);
                    border-color: rgba(247, 79, 117, 1);
                    color: #fff;
                }
            }
        }
    }
}

.addPage,
.card {
    @extend %float;
    margin-top: 50px;
    position: relative;
    z-index: 300;
    border: 1px solid #484863;
    border-radius: 15px;
    padding: 30px;
}

.form-group {
    @extend %float;
    label {
        margin-bottom: 15px;
        color: $dcol;
        font-size: 10pt;
        @extend %float;
    }
    .form-control {
        border: 1px solid #484863;
        color: #c0c2c7;
        background: none!important;
        outline: none;
        box-shadow: none;
        border-radius: 8px;
        padding: 12px 20px;
        font-size: 11pt;
        min-height: 48px;
        &:focus,
        &:hover {
            border-color: $mcol;
            color: $mcol;
        }
    }
}

.select-from-library-button,
.selected-library-item {
    border: 1px dashed #484863;
    border-radius: 10px;
    padding: 50px;
    text-align: center;
    line-height: 2;
    float: left;
    @extend %float;
    .card {
        margin: 0!important;
        padding: 0;
        border: none!important;
    }
    .list-media-thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        object-fit: cover;
    }
    .list-item-heading {
        text-overflow: ellipsis;
        overflow: hidden;
        width: 160px;
        height: 2em;
        white-space: nowrap;
    }
    .btn-link {
        width: 40px;
        height: 40px;
        @extend %flex;
        border-radius: 8px;
        background-color: rgba(247, 79, 117, .2);
        color: rgba(247, 79, 117, 1);
        transition: 0.3s all ease-in;
        &:hover {
            background-color: rgba(247, 79, 117, 1);
            color: #fff;
            text-decoration: none;
        }
    }
}

.compo {
    @extend %float;
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #484863;
    margin-top: 50px;
    &-item {
        @extend %float;
        border-radius: 15px;
        padding: 30px;
        background: #484863;
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        figure {
            width: 100px;
            height: 100px;
            border-radius: 15px;
            @extend %flex;
            background: rgba(56, 202, 179, .5);
            margin-right: 20px;
            svg {
                width: 48px;
                height: 48px;
                path {
                    stroke: $mcol;
                }
            }
        }
        &__title {
            display: flex;
            flex-direction: column;
            width: calc(100% - 120px);
            a {
                float: left;
            }
            h3 {
                text-transform: uppercase;
                font-size: 16px;
                color: #fff;
                margin-bottom: 20px;
                line-height: 1.5;
            }
            @media(max-width:1640px) {
                h3 {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 110px;
                    height: 1.6em;
                    white-space: nowrap;
                }
            }
        }
        a.btn-primary {
            @extend %flex;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            padding: 0;
            svg {
                width: 18px;
                height: 18px;
                path {
                    stroke: #fff;
                }
                polygon {
                    stroke: #fff;
                }
            }
        }
        a.btn-danger {
            @extend %flex;
            margin-left: 15px;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            padding: 0;
            svg {
                width: 18px;
                height: 18px;
                path {
                    stroke: rgba(247, 79, 117, 1);
                    ;
                }
            }
        }
    }
}

.btn-fixed {
    position: fixed;
    bottom: 30px;
    right: 45px;
    padding: 15px 30px;
}

.alert-for-component {
    @extend %flex;
    @extend %float;
    svg {
        max-width: 80%;
    }
}

.forus {
    @extend %float;
    margin-top: 40px;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, .1);
    margin-bottom: 40px;
    h3 {
        margin-bottom: 40px;
    }
    li {
        i {
            margin-right: 10px;
        }
    }
}

.data-table td,
.data-table th {
    border-top: 1px solid #484863!important;
    border-color: #484863!important;
    padding: 15px!important;
    vertical-align: middle;
    img {
        border-radius: 10px;
        object-fit: cover;
    }
}

.data-table {
    .remove-item {
        margin-left: 10px;
    }
}

.modal {
    z-index: 9999999;
}

.modal-backdrop {
    background: rgba(1, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    opacity: 1 !important;
}

.modal .modal-dialog {
    max-width: 536px;
    border: none;
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
}

.modal-content {
    border-radius: 20px;
    overflow: hidden;
    background: #2a2e3f;
}

.modal .smallbody {
    padding: 40px 30px;
}

.modal .modal-header {
    border: none;
    display: flex;
    padding-top: 40px;
    padding-left: 30px;
}

.modal .modal-header h5 {
    width: 100%;
    float: left;
    font-size: 19px;
    text-align: left;
    padding-right: 30px;
    color: #fff;
}

.modal .close {
    width: 50px;
    height: 50px;
    background: $mcol;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    opacity: 1;
    font-size: 26px;
    text-shadow: none;
    right: 40px;
    top: 40px;
    position: absolute;
}

.modal-body {
    padding: 0 30px;
    padding-top: 30px;
    .btn-add {
        width: auto!important;
        padding: 0 20px!important;
        font-size: 13px;
        svg {
            margin-right: 10px;
            path {
                stroke: none;
                fill: #fff;
            }
        }
    }
}

.card-top-buttons {
    @extend %float;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 15px;
    a {
        color: #fff;
        display: flex;
        align-items: center;
        background: #2a2e3f;
        box-shadow: none;
        text-transform: uppercase;
        margin-left: 20px;
        img {
            margin-right: 10px;
        }
        &.active {
            color: #fff;
            @extend %mfont;
            background: $mcol;
        }
    }
}

.cke_chrome {
    border: none!important;
    ;
}

.cke_inner {
    background: transparent!important;
}

.table thead th,
.table td,
.table th {
    border: none;
    border: 1px solid #2a2e3f;
    vertical-align: middle;
}

.card-header {
    border: none;
    padding: 0;
    background: none;
}

.collapse-title {
    background: none;
    display: flex;
    align-items: center;
    font-size: 14px;
    @extend %mfont;
    justify-content: space-between;
    cursor: pointer;
    padding: 10px 0;
    svg {
        width: 16px;
        height: 16px;
        path {
            fill: #fff;
        }
    }
}

.page_add {
    .page-title__item {
        padding-left: 30px;
    }
    .page-content__item {
        padding-left: 30px;
    }
    img {
        border-radius: 10px;
    }
}

.dd-handle {
    border: none;
    background: #2a2e3f;
    outline: none;
    padding: 15px 30px;
    border-radius: 10px;
    position: relative;
    height: auto;
    color: #fff;
    margin-bottom: 20px;
    &:hover {
        background: $mcol;
        color: #fff;
        .washla {
            a {
                color: #fff;
            }
        }
    }
}

.form-control:disabled,
.form-control[readonly] {
    border: none;
    background: #2a2e3f;
    outline: none;
    padding: 15px 30px;
    color: #fff;
    opacity: 0.7;
    border-radius: 15px;
}

.dd-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.select2-container .select2-selection--single {
    height: 40px;
}

#accordionWrapa1 .card-body {
    padding: 30px 0;
}

.title-of {
    @extend %float;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h5 {
        margin: 0!important;
    }
}

.btn-addd {
    display: flex;
    align-items: center;
    svg {
        margin-right: 10px;
        width: 18px;
        height: 18px;
        line {
            stroke: #fff;
        }
    }
}

.title-compo {
    @extend %float;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
    padding: 0 15px;
    h1 {
        color: $mcol;
        font-size: 18px;
        @extend %mfont;
    }
}

.login-section-wrapper {
    @extend %regular;
    position: absolute;
    @extend %flex;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.loginn {
    @extend %float;
    position: relative;
    height: 100vh;
    background: $scol;
    img {
        @extend %img;
        position: absolute;
        opacity: 0.3!important;
        filter: blur(7px);
    }
    .login-title {
        @extend %mfont;
        color: $mcol;
        margin-bottom: 15px;
    }
    .login-wrapper {
        width: 450px;
        background: $scol;
        padding: 30px;
        border-radius: 15px;
    }
    .btn-primary {
        @extend %btn;
        background: $mcol;
        border-radius: 10px;
        border: none;
        &:hover {
            background: #fff;
            color: $mcol;
        }
    }
}

.welcome {
    @extend %float;
    margin-top: 80px;
    figure {
        @extend %float;
        height: 50vh;
        @extend %flex;
        svg {
            max-width: 80%;
            height: 100%;
        }
    }
}

.vue-dropzone {
    @extend %mfont;
    font-size: 16px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #c5cfcd;
}

.dropzone {
    background: none!important;
    background-color: none!important;
    border: none;
    border: 2px dashed rgba(255, 255, 255, .1)!important;
    border-radius: 15px;
    overflow: hidden;
}

.list {
    img {
        width: 100%;
    }
}

.media-thumb-container {
    border-radius: 15px;
    overflow: hidden;
    padding: 5px;
    @extend %float;
}

@media (max-width:1024px) {
    .menu {
        display: none;
    }
    main {
        width: 100%;
    }
    .header-top__logo {
        border: none;
        width: 100%;
    }
    .header-top__actions {
        display: none;
    }
}