<?php

namespace Sina\Shuttle\FormFields;

class HiddenHandler extends <PERSON>bstract<PERSON><PERSON><PERSON>
{
    protected $codename = 'hidden';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.hidden', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
