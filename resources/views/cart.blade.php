@extends('app')

@section('content')

    @include('includes.header')
    <script src="https://js.stripe.com/v3/"></script>
    <section class="speadbar">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="speadbar-title">
                        <ul>
                            <li><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M18.4384 20C19.3561 20 20.1493 19.3726 20.2725 18.4632C20.3895 17.5988 20.5 16.4098 20.5 15C20.5 12 20.6683 10.1684 17.5 7C16.0386 5.53865 14.4064 4.41899 13.3024 3.74088C12.4978 3.24665 11.5021 3.24665 10.6975 3.74088C9.5935 4.41899 7.96131 5.53865 6.49996 7C3.33157 10.1684 3.49997 12 3.49997 15C3.49997 16.4098 3.61039 17.5988 3.72745 18.4631C3.85061 19.3726 4.64378 20 5.56152 20H18.4384Z"
                                        stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                    </path>
                                </svg></li>
                            <li class=""><a href="/" title="@lang('common.home')">@lang('common.home')</a></li>
                            <li class=" active "><a href="who-we-are" title="@lang('common.cart')">@lang('common.cart')</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section steps">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="steps-lists">
                        <div class="steps-lists__item active">
                            <p>Cart</p>
                        </div>
                        <div class="steps-lists__item" id="checkout-head">
                            <p>Checkout</p>
                        </div>
                        <div class="steps-lists__item">
                            <p>Comfirmation</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @php $totalPrice = 0; @endphp
    <section class="section cart-block d-block position-relative ">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="row">
                        <form action="{{ route('transaction.store') }}" method="post" class="col-lg-8" id="checkout-form">
                            @csrf
                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="cart" role="tabpanel"
                                    aria-labelledby="home-tab">
                                    <div class="section-title">
                                        <h2>My Cart ( {{ $cart->count() }} Course ) </h2>
                                    </div>
                                    @foreach ($cart as $c)
                                        @php $totalPrice += $c->courseLocation->price * $c->qty @endphp
                                        <div class="cart-item">
                                            <div class="cart-item__main">
                                                <div class="one-item swiper-container-horizontal">
                                                    <div class="name">
                                                        <figure>
                                                            <img src="{{ Storage::url($c->courseLocation->course->image) }}"
                                                                alt="" />
                                                        </figure>
                                                        <div class="name-infos">
                                                            <h4>{{ $c->courseLocation->course->title }}</h4>
                                                            <div class="c-t__infos">
                                                                <div class="country">
                                                                    <img src="{{ Storage::url($c->courseLocation->location->image) }}"
                                                                        alt="">
                                                                    <h2>{{ $c->courseLocation->location->name }}</h2>
                                                                </div>
                                                                <div class="date">
                                                                    <span>
                                                                        <svg width="800px" height="800px"
                                                                            viewBox="0 0 24 24" fill="none"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M20 10V7C20 5.89543 19.1046 5 18 5H6C4.89543 5 4 5.89543 4 7V10M20 10V19C20 20.1046 19.1046 21 18 21H6C4.89543 21 4 20.1046 4 19V10M20 10H4M8 3V7M16 3V7"
                                                                                stroke="#000000" stroke-width="2"
                                                                                stroke-linecap="round"></path>
                                                                            <rect x="6" y="12" width="3" height="3"
                                                                                rx="0.5" fill="#000000"></rect>
                                                                            <rect x="10.5" y="12" width="3"
                                                                                height="3" rx="0.5"
                                                                                fill="#000000"></rect>
                                                                            <rect x="15" y="12" width="3"
                                                                                height="3" rx="0.5"
                                                                                fill="#000000"></rect>
                                                                        </svg>
                                                                        {{ $c->courseLocation->upComingFormat }}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- /.one-item -->
                                                <div class="qty">
                                                    <div class="number">
                                                        <span class="minus">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14"
                                                                height="14" viewBox="0 0 14 14">
                                                                <g id="vuesax_linear_arrow-down"
                                                                    data-name="vuesax/linear/arrow-down"
                                                                    transform="translate(250 266) rotate(180)">
                                                                    <g id="arrow-down" transform="translate(236 252)">
                                                                        <path id="Vector"
                                                                            d="M9.24,0l-3.8,3.8A1.158,1.158,0,0,1,3.8,3.8L0,0"
                                                                            transform="translate(2.38 5.221)" fill="none"
                                                                            stroke="#404040" stroke-linecap="round"
                                                                            stroke-linejoin="round" stroke-width="1.5">
                                                                        </path>
                                                                        <path id="Vector-2" data-name="Vector"
                                                                            d="M0,0H14V14H0Z"
                                                                            transform="translate(14 14) rotate(180)"
                                                                            fill="none" opacity="0"></path>
                                                                    </g>
                                                                </g>
                                                            </svg>
                                                        </span>
                                                        <input class="qt-input no-popup" data-id="{{ $c->id }}"
                                                            data-route="{{ route('cart.update', $c) }}" type="text"
                                                            value="{{ $c->qty }}">
                                                        <span class="plus">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14"
                                                                height="14" viewBox="0 0 14 14">
                                                                <g id="vuesax_linear_arrow-down"
                                                                    data-name="vuesax/linear/arrow-down"
                                                                    transform="translate(250 266) rotate(180)">
                                                                    <g id="arrow-down" transform="translate(236 252)">
                                                                        <path id="Vector"
                                                                            d="M9.24,0l-3.8,3.8A1.158,1.158,0,0,1,3.8,3.8L0,0"
                                                                            transform="translate(2.38 5.221)"
                                                                            fill="none" stroke="#404040"
                                                                            stroke-linecap="round" stroke-linejoin="round"
                                                                            stroke-width="1.5"></path>
                                                                        <path id="Vector-2" data-name="Vector"
                                                                            d="M0,0H14V14H0Z"
                                                                            transform="translate(14 14) rotate(180)"
                                                                            fill="none" opacity="0"></path>
                                                                    </g>
                                                                </g>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="price">
                                                    <span>£ {{ $c->courseLocation->price }}</span>
                                                </div>
                                                <div class="act">
                                                    <button type="button" class="remove"
                                                        data-url="{{ route('cart.destroy', $c) }}"
                                                        style="background:none; border:none; padding:0; cursor:pointer;">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                            height="24" viewBox="0 0 24 24">
                                                            <g id="vuesax_linear_trash" data-name="vuesax/linear/trash"
                                                                transform="translate(-108 -188)">
                                                                <g id="trash">
                                                                    <path id="Vector"
                                                                        d="M18,.5C14.67.17,11.32,0,7.98,0A59.068,59.068,0,0,0,2.04.3L0,.5"
                                                                        transform="translate(111 193.48)" fill="none"
                                                                        stroke="#9f9f9f" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="1.5"></path>
                                                                    <path id="Vector-2" data-name="Vector"
                                                                        d="M0,2.97.22,1.66C.38.71.5,0,2.19,0H4.81C6.5,0,6.63.75,6.78,1.67L7,2.97"
                                                                        transform="translate(116.5 190)" fill="none"
                                                                        stroke="#9f9f9f" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="1.5"></path>
                                                                    <path id="Vector-3" data-name="Vector"
                                                                        d="M13.7,0l-.65,10.07c-.11,1.57-.2,2.79-2.99,2.79H3.64C.85,12.86.76,11.64.65,10.07L0,0"
                                                                        transform="translate(113.15 197.14)"
                                                                        fill="none" stroke="#9f9f9f"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        stroke-width="1.5"></path>
                                                                    <path id="Vector-4" data-name="Vector" d="M0,0H3.33"
                                                                        transform="translate(118.33 204.5)" fill="none"
                                                                        stroke="#9f9f9f" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="1.5"></path>
                                                                    <path id="Vector-5" data-name="Vector" d="M0,0H5"
                                                                        transform="translate(117.5 200.5)" fill="none"
                                                                        stroke="#9f9f9f" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="1.5"></path>
                                                                    <path id="Vector-6" data-name="Vector"
                                                                        d="M0,0H24V24H0Z" transform="translate(108 188)"
                                                                        fill="none" opacity="0"></path>
                                                                </g>
                                                            </g>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="tab-pane fade" id="checkout" role="tabpanel" aria-labelledby="profile-tab">
                                    <div class="c-form">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="section-title">
                                                    <h2>@lang('common.ur_details')</h2>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="loginform-form">
                                                    <!-- Hidden fields to store price, vat, and subtotal -->
                                                    <input type="hidden" name="total_price" id="total-price"
                                                        value="{{ old('total_price', $totalPrice) }}">
                                                    <input type="hidden" name="vat_price" id="vat-price"
                                                        value="{{ old('vat_price', $totalPrice * 0.2) }}">
                                                    <input type="hidden" name="subtotal_price" id="subtotal-price"
                                                        value="{{ old('subtotal_price', $totalPrice * 1.2) }}">
                                                    <input name="name" class="f-input" type="text"
                                                        placeholder="@lang('common.full_name')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->
                                            <div class="col-md-6">
                                                <div class="loginform-form">
                                                    <input name="company" class="f-input" type="text"
                                                        placeholder="@lang('common.company_name')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->

                                            <div class="col-md-6">
                                                <div class="loginform-form">
                                                    <input name="job" class="f-input" type="text"
                                                        placeholder="@lang('common.jobs_title')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->


                                            <div class="col-md-6">
                                                <div class="loginform-form">
                                                    <input name="email" class="f-input" type="mail"
                                                        placeholder="@lang('common.mail')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->

                                            <div class="col-md-6">
                                                <div class="loginform-form">
                                                    <input name="mobile" class="f-input" type="number"
                                                        placeholder="@lang('common.number')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->

                                            <div class="col-md-12">
                                                <div class="loginform-form">
                                                    <input name="address1" class="f-input" type="text"
                                                        placeholder="@lang('common.street_line1')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->

                                            <div class="col-md-12">
                                                <div class="loginform-form">
                                                    <input name="address2" class="f-input" type="text"
                                                        placeholder="@lang('common.street_line2')">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->


                                            <div class="col-md-6">
                                                <div class="loginform-form">
                                                    <input name="city" class="f-input" type="text"
                                                        placeholder="@lang('common.city')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->


                                            <div class="col-md-6">
                                                <div class="loginform-form">
                                                    <input name="zip_code" class="f-input" type="text"
                                                        placeholder="@lang('common.zip_code')" data-validation="required">
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->

                                            <div class="col-md-12">
                                                <div class="loginform-form">
                                                    <select name="country" class="select">
                                                        <option value="">@lang('common.country')</option>
                                                        <option value="United Kingdom" selected>United Kingdom</option>
                                                        <option value="AFGHANISTAN">AFGHANISTAN</option>
                                                        <option value="ALBANIA">ALBANIA</option>
                                                        <option value="ALGERIA">ALGERIA</option>
                                                        <option value="AMERICAN SAMOA">AMERICAN SAMOA</option>
                                                        <option value="ANDORRA">ANDORRA</option>
                                                        <option value="ANGOLA">ANGOLA</option>
                                                        <option value="ANGUILLA">ANGUILLA</option>
                                                        <option value="ANTARCTICA">ANTARCTICA</option>
                                                        <option value="ANTIGUA AND BARBUDA">ANTIGUA AND BARBUDA</option>
                                                        <option value="ARGENTINA">ARGENTINA</option>
                                                        <option value="ARMENIA">ARMENIA</option>
                                                        <option value="ARUBA">ARUBA</option>
                                                        <option value="AUSTRALIA">AUSTRALIA</option>
                                                        <option value="AUSTRIA">AUSTRIA</option>
                                                        <option value="AZERBAIJAN">AZERBAIJAN</option>
                                                        <option value="BAHAMAS">BAHAMAS</option>
                                                        <option value="BAHRAIN">BAHRAIN</option>
                                                        <option value="BANGLADESH">BANGLADESH</option>
                                                        <option value="BARBADOS">BARBADOS</option>
                                                        <option value="BELARUS">BELARUS</option>
                                                        <option value="BELGIUM">BELGIUM</option>
                                                        <option value="BELIZE">BELIZE</option>
                                                        <option value="BENIN">BENIN</option>
                                                        <option value="BERMUDA">BERMUDA</option>
                                                        <option value="BHUTAN">BHUTAN</option>
                                                        <option value="BOLIVIA">BOLIVIA</option>
                                                        <option value="BOSNIA AND HERZEGOVINA">BOSNIA AND HERZEGOVINA
                                                        </option>
                                                        <option value="BOTSWANA">BOTSWANA</option>
                                                        <option value="BOUVET ISLAND">BOUVET ISLAND</option>
                                                        <option value="BRAZIL">BRAZIL</option>
                                                        <option value="BRITISH INDIAN OCEAN TERRITORY">BRITISH INDIAN OCEAN
                                                            TERRITORY</option>
                                                        <option value="BRUNEI DARUSSALAM">BRUNEI DARUSSALAM</option>
                                                        <option value="BULGARIA">BULGARIA</option>
                                                        <option value="BURKINA FASO">BURKINA FASO</option>
                                                        <option value="35">BURUNDI</option>
                                                        <option value="36">CAMBODIA</option>
                                                        <option value="37">CAMEROON</option>
                                                        <option value="38">CANADA</option>
                                                        <option value="39">CAPE VERDE</option>
                                                        <option value="40">CAYMAN ISLANDS</option>
                                                        <option value="41">CENTRAL AFRICAN REPUBLIC</option>
                                                        <option value="42">CHAD</option>
                                                        <option value="43">CHILE</option>
                                                        <option value="44">CHINA</option>
                                                        <option value="45">CHRISTMAS ISLAND</option>
                                                        <option value="46">COCOS (KEELING) ISLANDS</option>
                                                        <option value="47">COLOMBIA</option>
                                                        <option value="48">COMOROS</option>
                                                        <option value="49">CONGO</option>
                                                        <option value="50">CONGO, THE DEMOCRATIC REPUBLIC OF THE
                                                        </option>
                                                        <option value="51">COOK ISLANDS</option>
                                                        <option value="52">COSTA RICA</option>
                                                        <option value="53">COTE D'IVOIRE</option>
                                                        <option value="54">CROATIA</option>
                                                        <option value="55">CUBA</option>
                                                        <option value="56">CYPRUS</option>
                                                        <option value="57">CZECH REPUBLIC</option>
                                                        <option value="58">DENMARK</option>
                                                        <option value="59">DJIBOUTI</option>
                                                        <option value="60">DOMINICA</option>
                                                        <option value="61">DOMINICAN REPUBLIC</option>
                                                        <option value="62">ECUADOR</option>
                                                        <option value="63">EGYPT</option>
                                                        <option value="64">EL SALVADOR</option>
                                                        <option value="65">EQUATORIAL GUINEA</option>
                                                        <option value="66">ERITREA</option>
                                                        <option value="67">ESTONIA</option>
                                                        <option value="68">ETHIOPIA</option>
                                                        <option value="69">FALKLAND ISLANDS (MALVINAS)</option>
                                                        <option value="70">FAROE ISLANDS</option>
                                                        <option value="71">FIJI</option>
                                                        <option value="72">FINLAND</option>
                                                        <option value="73">FRANCE</option>
                                                        <option value="74">FRENCH GUIANA</option>
                                                        <option value="75">FRENCH POLYNESIA</option>
                                                        <option value="76">FRENCH SOUTHERN TERRITORIES</option>
                                                        <option value="77">GABON</option>
                                                        <option value="78">GAMBIA</option>
                                                        <option value="79">GEORGIA</option>
                                                        <option value="80">GERMANY</option>
                                                        <option value="81">GHANA</option>
                                                        <option value="82">GIBRALTAR</option>
                                                        <option value="83">GREECE</option>
                                                        <option value="84">GREENLAND</option>
                                                        <option value="85">GRENADA</option>
                                                        <option value="86">GUADELOUPE</option>
                                                        <option value="87">GUAM</option>
                                                        <option value="88">GUATEMALA</option>
                                                        <option value="89">GUINEA</option>
                                                        <option value="90">GUINEA-BISSAU</option>
                                                        <option value="91">GUYANA</option>
                                                        <option value="92">HAITI</option>
                                                        <option value="93">HEARD ISLAND AND MCDONALD ISLANDS</option>
                                                        <option value="94">HOLY SEE (VATICAN CITY STATE)</option>
                                                        <option value="95">HONDURAS</option>
                                                        <option value="96">HONG KONG</option>
                                                        <option value="97">HUNGARY</option>
                                                        <option value="98">ICELAND</option>
                                                        <option value="99">INDIA</option>
                                                        <option value="100">INDONESIA</option>
                                                        <option value="101">IRAN, ISLAMIC REPUBLIC OF</option>
                                                        <option value="102">IRAQ</option>
                                                        <option value="103">IRELAND</option>
                                                        <option value="104">ISRAEL</option>
                                                        <option value="105">ITALY</option>
                                                        <option value="106">JAMAICA</option>
                                                        <option value="107">JAPAN</option>
                                                        <option value="108">JORDAN</option>
                                                        <option value="109">KAZAKHSTAN</option>
                                                        <option value="110">KENYA</option>
                                                        <option value="111">KIRIBATI</option>
                                                        <option value="112">KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF
                                                        </option>
                                                        <option value="113">KOREA, REPUBLIC OF</option>
                                                        <option value="114">KUWAIT</option>
                                                        <option value="115">KYRGYZSTAN</option>
                                                        <option value="116">LAO PEOPLE'S DEMOCRATIC REPUBLIC</option>
                                                        <option value="117">LATVIA</option>
                                                        <option value="118">LEBANON</option>
                                                        <option value="119">LESOTHO</option>
                                                        <option value="120">LIBERIA</option>
                                                        <option value="121">LIBYAN ARAB JAMAHIRIYA</option>
                                                        <option value="122">LIECHTENSTEIN</option>
                                                        <option value="123">LITHUANIA</option>
                                                        <option value="124">LUXEMBOURG</option>
                                                        <option value="125">MACAO</option>
                                                        <option value="126">MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF
                                                        </option>
                                                        <option value="127">MADAGASCAR</option>
                                                        <option value="128">MALAWI</option>
                                                        <option value="129">MALAYSIA</option>
                                                        <option value="130">MALDIVES</option>
                                                        <option value="131">MALI</option>
                                                        <option value="132">MALTA</option>
                                                        <option value="133">MARSHALL ISLANDS</option>
                                                        <option value="134">MARTINIQUE</option>
                                                        <option value="135">MAURITANIA</option>
                                                        <option value="136">MAURITIUS</option>
                                                        <option value="137">MAYOTTE</option>
                                                        <option value="138">MEXICO</option>
                                                        <option value="139">MICRONESIA, FEDERATED STATES OF</option>
                                                        <option value="140">MOLDOVA, REPUBLIC OF</option>
                                                        <option value="141">MONACO</option>
                                                        <option value="142">MONGOLIA</option>
                                                        <option value="143">MONTSERRAT</option>
                                                        <option value="144">MOROCCO</option>
                                                        <option value="145">MOZAMBIQUE</option>
                                                        <option value="146">MYANMAR</option>
                                                        <option value="147">NAMIBIA</option>
                                                        <option value="148">NAURU</option>
                                                        <option value="149">NEPAL</option>
                                                        <option value="150">NETHERLANDS</option>
                                                        <option value="151">NETHERLANDS ANTILLES</option>
                                                        <option value="152">NEW CALEDONIA</option>
                                                        <option value="153">NEW ZEALAND</option>
                                                        <option value="154">NICARAGUA</option>
                                                        <option value="155">NIGER</option>
                                                        <option value="156">NIGERIA</option>
                                                        <option value="157">NIUE</option>
                                                        <option value="158">NORFOLK ISLAND</option>
                                                        <option value="159">NORTHERN MARIANA ISLANDS</option>
                                                        <option value="160">NORWAY</option>
                                                        <option value="161">OMAN</option>
                                                        <option value="162">PAKISTAN</option>
                                                        <option value="163">PALAU</option>
                                                        <option value="164">PALESTINIAN TERRITORY, OCCUPIED</option>
                                                        <option value="165">PANAMA</option>
                                                        <option value="166">PAPUA NEW GUINEA</option>
                                                        <option value="167">PARAGUAY</option>
                                                        <option value="168">PERU</option>
                                                        <option value="169">PHILIPPINES</option>
                                                        <option value="170">PITCAIRN</option>
                                                        <option value="171">POLAND</option>
                                                        <option value="172">PORTUGAL</option>
                                                        <option value="173">PUERTO RICO</option>
                                                        <option value="174">QATAR</option>
                                                        <option value="175">REUNION</option>
                                                        <option value="176">ROMANIA</option>
                                                        <option value="177">RUSSIAN FEDERATION</option>
                                                        <option value="178">RWANDA</option>
                                                        <option value="179">SAINT HELENA</option>
                                                        <option value="180">SAINT KITTS AND NEVIS</option>
                                                        <option value="181">SAINT LUCIA</option>
                                                        <option value="182">SAINT PIERRE AND MIQUELON</option>
                                                        <option value="183">SAINT VINCENT AND THE GRENADINES</option>
                                                        <option value="184">SAMOA</option>
                                                        <option value="185">SAN MARINO</option>
                                                        <option value="186">SAO TOME AND PRINCIPE</option>
                                                        <option value="187">SAUDI ARABIA</option>
                                                        <option value="188">SENEGAL</option>
                                                        <option value="189">SERBIA AND MONTENEGRO</option>
                                                        <option value="190">SEYCHELLES</option>
                                                        <option value="191">SIERRA LEONE</option>
                                                        <option value="192">SINGAPORE</option>
                                                        <option value="193">SLOVAKIA</option>
                                                        <option value="194">SLOVENIA</option>
                                                        <option value="195">SOLOMON ISLANDS</option>
                                                        <option value="196">SOMALIA</option>
                                                        <option value="197">SOUTH AFRICA</option>
                                                        <option value="198">SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS
                                                        </option>
                                                        <option value="199">SPAIN</option>
                                                        <option value="200">SRI LANKA</option>
                                                        <option value="201">SUDAN</option>
                                                        <option value="202">SURINAME</option>
                                                        <option value="203">SVALBARD AND JAN MAYEN</option>
                                                        <option value="204">SWAZILAND</option>
                                                        <option value="205">SWEDEN</option>
                                                        <option value="206">SWITZERLAND</option>
                                                        <option value="207">SYRIAN ARAB REPUBLIC</option>
                                                        <option value="208">TAIWAN, PROVINCE OF CHINA</option>
                                                        <option value="209">TAJIKISTAN</option>
                                                        <option value="210">TANZANIA, UNITED REPUBLIC OF</option>
                                                        <option value="211">THAILAND</option>
                                                        <option value="212">TIMOR-LESTE</option>
                                                        <option value="213">TOGO</option>
                                                        <option value="214">TOKELAU</option>
                                                        <option value="215">TONGA</option>
                                                        <option value="216">TRINIDAD AND TOBAGO</option>
                                                        <option value="217">TUNISIA</option>
                                                        <option value="218">TURKEY</option>
                                                        <option value="219">TURKMENISTAN</option>
                                                        <option value="220">TURKS AND CAICOS ISLANDS</option>
                                                        <option value="221">TUVALU</option>
                                                        <option value="222">UGANDA</option>
                                                        <option value="223">UKRAINE</option>
                                                        <option value="224">UNITED ARAB EMIRATES</option>
                                                        <option value="225">UNITED KINGDOM</option>
                                                        <option value="226">UNITED STATES</option>
                                                        <option value="227">UNITED STATES MINOR OUTLYING ISLANDS
                                                        </option>
                                                        <option value="228">URUGUAY</option>
                                                        <option value="229">UZBEKISTAN</option>
                                                        <option value="230">VANUATU</option>
                                                        <option value="231">VENEZUELA</option>
                                                        <option value="232">VIET NAM</option>
                                                        <option value="233">VIRGIN ISLANDS, BRITISH</option>
                                                        <option value="234">VIRGIN ISLANDS, U.S.</option>
                                                        <option value="235">WALLIS AND FUTUNA</option>
                                                        <option value="236">WESTERN SAHARA</option>
                                                        <option value="237">YEMEN</option>
                                                        <option value="238">ZAMBIA</option>
                                                        <option value="239">ZIMBABWE</option>
                                                    </select>
                                                    <!-- /#.select -->
                                                </div>
                                            </div>
                                            <!-- /.col-md-6 -->
                                            <!-- /.col-md-6 -->

                                            <div
                                                class="col-md-12 mb-3  d-flex justify-content-between align-items-center">
                                                <h2 class="mb-0"
                                                    style="font-weight: 500;font-size: 24px;line-height: 130%;font-family: p_bold">
                                                    Payment Details</h2>
                                                <img src="https://www.longislandmarinatn.com/wp-content/uploads/2017/08/Major-Credit-Card-Logo-PNG-Image.png"
                                                    alt="Card Icons" style="height: 40px;">
                                            </div>
                                            <div class="col-md-12">
                                                <small class="text-muted">Enter your card details below. Your payment is
                                                    secure and encrypted.</small>
                                                <div class="mt-5 loginform-form"
                                                    style="padding: 12px 20px;border-radius: 5px;border: 2px solid #f2f2f2;"
                                                    id="card-element"></div>
                                            </div>
                                            <!-- /.col-md-6 -->
                                        </div>
                                        <!-- /.row -->
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="contact-tab">
                                    ...</div>
                            </div>
                        </form>

                        <div class="col-lg-4">
                            <div class="all">
                                <h1>Ticket</h1>
                                <ul>
                                    <li>
                                        <span>@lang('common.total')</span>
                                        <p class="cart-total-price">£ {{ number_format($totalPrice, 2) }}</p>
                                    </li>

                                    <li>
                                        <span>@lang('common.vat') 20%</span>
                                        <p class="cart-vat-price">£ {{ number_format($totalPrice * 0.2, 2) }}</p>
                                    </li>

                                    <li>
                                        <span>@lang('common.subtotal')</span>
                                        <p class="cart-subtotal-price total-price" data-price="{{ number_format($totalPrice * 1.2, 2) }}">£ {{ number_format($totalPrice * 1.2, 2) }}</p>
                                        <!-- DEBUG: totalPrice={{ $totalPrice }} -->
                                    </li>

                                </ul>
                                <div class="promo">
                                    <h1>Promo</h1>
                                    <div class="have">
                                        <h2>Do u Have Promo?</h2>
                                        <label class="check-1">
                                            <input id="promocode" type="checkbox">
                                            <div class="inner"></div>
                                            <div class="bullet"></div>
                                        </label>
                                    </div>
                                    <div id="promo-input" class="d-none">
                                        <input type="text" name="promo_code" placeholder="Promo Code" />
                                        <button type="button" id="promo-submit-btn">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="46" height="46"
                                                viewBox="0 0 46 46">
                                                <g id="Group_1775" data-name="Group 1775"
                                                    transform="translate(-1679 -488)">
                                                    <rect id="Rectangle_164" data-name="Rectangle 164" width="46"
                                                        height="46" rx="10" transform="translate(1679 488)"
                                                        fill="#f3f3f3"></rect>
                                                    <g id="_3917090" data-name="3917090"
                                                        transform="translate(1692 501)">
                                                        <path id="Path_1671" data-name="Path 1671"
                                                            d="M6.933,11.847,4.686,9.6a.573.573,0,0,0-.811,0h0a.573.573,0,0,0,0,.811l2.247,2.247a1.147,1.147,0,0,0,1.622,0L13.05,7.353a.573.573,0,0,0,0-.811h0a.573.573,0,0,0-.811,0Z"
                                                            transform="translate(1.293 0.626)" fill="#404040"></path>
                                                    </g>
                                                </g>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="agreement">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="pm1"
                                            name="pm">
                                        <label class="custom-control-label" for="pm1">@lang('common.agree') <a
                                                href="/terms-and-conditions">@lang('common.terms')</a></label>
                                    </div>
                                </div>
                                <a href="#" class="btn-dark inv d-none" id="payInvoice">
                                    <span>Pay By Invoice</span>
                                    <svg fill="#000000" width="800px" height="800px" viewBox="0 0 16 16"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M5.44 7.47h5.26v1.25H5.44zm0 2.36h5.26v1.25H5.44zm0-4.76h5.26v1.25H5.44z" />
                                        <path
                                            d="M11.34 1 9.64.28 8.08 1 6.41.28 4.84 1 2.46 0v16l2.38-1 1.57.69L8.08 15l1.56.69 1.7-.69 2.2 1V0zm.94 13.11-.92-.41-1.69.69-1.57-.72-1.68.69-1.55-.69-1.15.47V1.86l1.15.47 1.55-.69 1.68.69 1.57-.69 1.69.69.92-.41z" />
                                    </svg>
                                </a>
                                @auth
                                    <a href="#" class="btn-dark ch" id="go-checkout">
                                        <span id="btn-text">Checkout</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="13.5" height="12.121"
                                            viewBox="0 0 13.5 12.121">
                                            <g transform="translate(129.25 400.061) rotate(180)">
                                                <path d="M10.286,0H0" transform="translate(118.214 394)" fill="none"
                                                    stroke="#fff" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="1.5" />
                                                <path d="M5.143,0,0,5l5.143,5" transform="translate(116.5 389)" fill="none"
                                                    stroke="#fff" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="1.5" />
                                            </g>
                                        </svg>
                                    </a>
                                @else
                                    <a href="{{ route('login') }}" class="btn btn-primary">
                                        Login to Checkout
                                    </a>
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const stripe = Stripe(
                "pk_test_51LxEwJLCJ2NwvyLdyC9zUiYUSSAlagLKKcpdgaCxygytUuA7yUSipFcXr0YZaZ24PIk8AH1D3zN0WH1Kzh9JICME00B5XvAKVc"
            ); // or use your publishable key
            const elements = stripe.elements();
            const style = {
                base: {
                    color: "#32325d",
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    fontSize: "16px",
                    border: "2px solid black",
                    '::placeholder': {
                        color: "#aab7c4"
                    }
                },
                invalid: {
                    color: "#fa755a",
                }
            };

            const classes = {
                base: 'f-input' // Optional, if you want to use your custom input class
            };

            const card = elements.create('card', {
                style: style,
                classes: classes
            });

            card.mount('#card-element');

            $("#go-checkout").on('click', function(e) {
                e.preventDefault();

                if ($('#checkout').hasClass('active')) {
                    const form = document.getElementById('checkout-form');
                    const inputs = form.querySelectorAll(
                        'input[data-validation="required"], select[data-validation="required"], textarea[data-validation="required"]'
                    );

                    let hasEmpty = false;

                    inputs.forEach(function(input) {
                        if (!input.value.trim()) {
                            input.style.border = '2px solid red'; // optional visual feedback
                            hasEmpty = true;
                        } else {
                            input.style.border = ''; // reset
                        }
                    });

                    if (hasEmpty) {
                        alert('Please fill all required fields.');
                        return;
                    }


                    stripe.createToken(card).then(function(result) {
                        if (result.error) {
                            alert(result.error.message);
                        } else {
                            // Create a FormData object with all form data
                            const form = document.getElementById('checkout-form');
                            const formData = new FormData(form);

                            // Append Stripe token
                            formData.append('stripeToken', result.token.id);

                            // Optional: Show loading spinner
                            $("#go-checkout").prop('disabled', true).text("Processing...");

                            // Send AJAX POST to Laravel
                            $.ajax({
                                url: form.action,
                                type: 'POST',
                                data: formData,
                                contentType: false,
                                processData: false,
                                success: function(response) {
                                    console.log("Payment Success", response);
                                    //alert('Payment successful!');
                                    // You can redirect or show success message
                                    window.location.href = response
                                        .redirect_url; // Redirect after success
                                },
                                error: function(xhr) {
                                    console.log("Payment Error", xhr.responseText);
                                    alert('Payment failed. Please try again.');
                                },
                                complete: function() {
                                    $("#go-checkout").prop('disabled', false).text(
                                        "Pay Now");
                                }
                            });
                        }
                    });
                    return;
                }

                // Move to checkout tab
                $(".tab-pane").removeClass('active').removeClass('show');
                $('#payInvoice').removeClass('d-none');
                $('#checkout').addClass('active show');
                $("#btn-text").text('Payment');
                $("#checkout-head").addClass('active');
            });
        });
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.remove').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = this.getAttribute('data-url');

                    // if (confirm('Are you sure you want to remove this item?')) {
                    fetch(url, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            }
                        })
                        .then(response => {
                            if (response.ok) {
                                // Remove the cart item element from the DOM
                                this.closest('.cart-item').remove();
                                // Optionally reload or update totals
                                location.reload(); // Reload to update totals
                            } else {
                                alert('Failed to delete item.');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                    // }
                });
            });
        });
    </script>
    <script src="{{ asset('js/promo-code.js') }}"></script>

    <script>
    // Inline backup promo code validation
    function validatePromoCodeInline() {
        try {
            console.log('Inline promo validation started');

        const promoInput = document.querySelector('#promo-input input[name="promo_code"]');
        const submitBtn = document.querySelector('#promo-submit-btn');

        if (!promoInput || !promoInput.value.trim()) {
            alert('Please enter a promo code.');
            return false;
        }

        const code = promoInput.value.trim().toUpperCase();
        const priceElement = document.querySelector('.total-price');
        let amount = priceElement ? parseFloat(priceElement.getAttribute('data-price') || '0') : 0;

        console.log('Original amount from data-price:', amount);
        console.log('Using amount as-is (no conversion):', amount);

        console.log('Inline validation:', { code, amount });

        if (amount <= 0) {
            alert('Unable to calculate order amount.');
            return false;
        }

        // Show loading
        submitBtn.textContent = 'Validating...';
        submitBtn.disabled = true;

        // Make API call
        fetch('/api/promo-code/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({ code, amount })
        })
        .then(response => response.json())
        .then(data => {
            console.log('API response:', data);

            if (data.valid) {
                alert(`Success! ${data.message}\nDiscount: $${data.discount_amount}\nFinal Amount: $${data.final_amount}`);
            } else {
                alert(`Error: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error validating promo code. Please try again.');
        })
        .finally(() => {
            // Reset button
            submitBtn.textContent = '';
            submitBtn.disabled = false;
        });

        return false;

        } catch (error) {
            console.error('Promo validation error:', error);
            alert('Error: ' + error.message);
            return false;
        }
    }

    // Also handle Enter key
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up promo events');

        // Button click
        const promoBtn = document.querySelector('#promo-submit-btn');
        if (promoBtn) {
            console.log('Found promo button, adding click listener');
            promoBtn.addEventListener('click', function(e) {
                console.log('Promo button clicked!');
                e.preventDefault();
                e.stopPropagation();
                alert('Button works! No refresh!');
                return false;
            });
        } else {
            console.log('Promo button not found!');
        }

        // Enter key
        const promoInput = document.querySelector('#promo-input input[name="promo_code"]');
        if (promoInput) {
            promoInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    console.log('Enter key pressed');
                    e.preventDefault();
                    alert('Enter works! No refresh!');
                    return false;
                }
            });
        }
    });
    </script>

@stop
