@extends('shuttle::admin')

@php $typeOptions = collect(['input', 'textarea', 'country', 'checkbox', 'radio', 'select'])->map(fn($loc) => '<option value="'.$loc.'">'.$loc.'</option>')->implode('') @endphp

@section('main')
    <div class="row">
        <div class="col-md-12">
            <x-shuttle-form
                    :action="$dataTypeContent->id ? route('shuttle.scaffold_interface.update', ['scaffold_interface' => $scaffold_interface, 'id' => $dataTypeContent->id, 'lang' => $lang]) : route('shuttle.scaffold_interface.store',$scaffold_interface)"
                    :scaffold-interface-rows="$scaffold_interface->rows"
                    :data-type-content="$dataTypeContent"
                    :edit="$dataTypeContent->id ? true : false"
            >
                <x-slot name="fields">
                    <div class="form-group col-md-12 " id="media_lib_app">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                            <tr>
                                <th scope="col">Label</th>
                                <th scope="col">Type</th>
                                <th scope="col">Options</th>
                                <th class="text-right">
                                    <button class="btn btn-primary icon-button addbtn" type="button">
                                        <i class="simple-icon-plus"></i>
                                    </button>
                                </th>
                            </tr>
                            </thead>
                            <tbody id="other-chance-ticket-table">
                            @foreach ($dataTypeContent->fields ?? [] as $item)
                                <tr>
                                    <td>
                                        <input class="form-control" name="fields[{{$loop->index}}][label]" value="{{data_get($item, 'label')}}" />
                                    </td>
                                    <td>
                                        <select class="form-control country-select" name="fields[{{$loop->index}}][type]" data-value="{{data_get($item, 'type')}}">{!! $typeOptions !!}</select>
                                    </td>
                                    <td>
                                        <input class="form-control" name="fields[{{$loop->index}}][options]" value="{{data_get($item, 'options')}}" />
                                    </td>
                                    <td class="text-right">
                                        <button class="btn btn-danger icon-button trashBtn" type="button">
                                            <i class="simple-icon-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </x-slot>
            </x-shuttle-form>
        </div>
    </div>
@stop

@push('js')
    <script>
        $(".country-select").each(function(item) {
            $(this).val($(this).data('value')).change()
        })

        $('.addbtn').click(function(e) {
            e.preventDefault();
            addPlan();
        });

        $(document).on("click", '.trashBtn', function(e) {
            e.preventDefault();
            $(this).parents('tr').remove()
        })

        const uid = function(){
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function addPlan() {
            var id = uid();
            var $el = `<tr>
                        <td>
                            <input class="form-control" name="fields[${id}][label]" />
                        </td>
                        <td>
                            <select class="form-control country-select" name="fields[${id}][type]">{!! $typeOptions !!}</select>
                        </td>
                        <td>
                            <input class="form-control" name="fields[${id}][options]" value="" />
                        </td>
                        <td class="text-right">
                            <button class="btn btn-danger icon-button trashBtn" type="button">
                                <i class="simple-icon-trash"></i>
                            </button>
                        </td>
                    </tr>`;

            $('#other-chance-ticket-table').append($el);

        }
    </script>
@endpush