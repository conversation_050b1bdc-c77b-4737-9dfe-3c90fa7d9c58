"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[603],{8603:(t,s,a)=>{a.r(s),a.d(s,{default:()=>l});const l=(0,a(1900).Z)({},(function(){var t=this,s=t._self._c;return s("hash-modal",{attrs:{"modal-id":"filters"}},[s("div",{staticClass:"modal-header"},[s("h5",{staticClass:"modal-title"},[t._v("Modal title")]),t._v(" "),s("button",{staticClass:"close",attrs:{type:"button","data-dismiss":"modal","aria-label":"Close"}},[s("span",{attrs:{"aria-hidden":"true"}},[t._v("×")])])]),t._v(" "),s("div",{staticClass:"modal-body"},[s("p",[t._v("Modal body text goes here.")])]),t._v(" "),s("div",{staticClass:"modal-footer"},[s("button",{staticClass:"btn btn-primary",attrs:{type:"button"}},[t._v("Filter")]),t._v(" "),s("button",{staticClass:"btn btn-secondary",attrs:{type:"button","data-dismiss":"modal"}},[t._v("\n      Close\n    ")])])])}),[],!1,null,null,null).exports}}]);