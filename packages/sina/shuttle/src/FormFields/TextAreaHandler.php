<?php

namespace Sina\Shuttle\FormFields;

class TextAreaHandler extends <PERSON>bstract<PERSON><PERSON><PERSON>
{
    protected $codename = 'text_area';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.text_area', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
