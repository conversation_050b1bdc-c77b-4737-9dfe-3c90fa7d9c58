<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Models\Component;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Models\PageComponent;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ConferencePackage;
use App\Models\CourseLocation;
use Illuminate\Http\Request;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;

class TransactionController extends ShuttleController
{
    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface, function ($q){
                    return $q->latest();
                })
//                ->addAction(fn ($data) => '<a href="' . route('shuttle.conference.copy',  $data->id) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
//                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<a href="'.route('shuttle.scaffold_interface.show', ['scaffold_interface' => $scaffoldInterface, 'id' => $data->id]).'" class="btn btn-bootstrap-padding btn-primary show-detail"><i class="glyph-icon simple-icon-pencil"></i></a>')
        )->json();
    }

    public function show(Request $request, ScaffoldInterface $scaffold_interface, $id)
    {
        $transaction = Transaction::findOrFail($id);
        $data = [
            'myConference' => TransactionItem::query()
                ->where('model_type', ConferencePackage::class)
                ->where('transaction_id', $transaction->id)
                ->whereHas('model')
                ->with('model')
                ->get(),
            'myCourse' => TransactionItem::query()
                ->where('model_type', CourseLocation::class)
                ->where('transaction_id', $transaction->id)
                ->with('model')
                ->get(),
            'transaction' => $transaction,
        ];
        return view('shuttle.transactions.show', $data);
    }

}
