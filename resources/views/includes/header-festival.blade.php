<header class="header header-full">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="header-menu">
                    <div class="header-menu__logo">
                        <a href="/">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1284.000000pt" height="1058.000000pt" viewBox="0 0 1284.000000 1058.000000" preserveAspectRatio="xMidYMid meet">

                                <g transform="translate(0.000000,1058.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none">
                                    <path d="M5409 8514 c-80 -71 -174 -154 -210 -185 -35 -31 -224 -197 -419
                               -369 -195 -172 -440 -388 -544 -479 -104 -91 -196 -173 -205 -181 -9 -8 -144
                               -127 -301 -265 -157 -137 -328 -287 -380 -334 -52 -46 -108 -96 -125 -110 -16
                               -15 -187 -165 -380 -335 -192 -170 -399 -352 -460 -404 -226 -197 -277 -265
                               -311 -419 -20 -92 -14 -189 17 -281 l21 -63 27 24 c14 13 37 34 51 46 82 74
                               203 140 327 177 78 24 105 27 238 28 223 1 268 -15 570 -198 502 -304 578
                               -348 621 -363 62 -21 164 -13 224 17 25 13 122 90 215 173 94 82 313 275 487
                               428 174 153 345 311 379 351 115 134 199 284 253 458 41 128 72 368 47 358 -4
                               -1 -39 -31 -77 -64 -38 -34 -172 -153 -299 -264 -126 -111 -355 -313 -508
                               -449 -154 -135 -300 -259 -327 -274 -103 -60 -225 -43 -312 44 -29 29 -48 56
                               -46 67 3 14 224 215 503 455 33 29 85 75 115 102 30 28 89 80 130 115 41 36
                               77 67 80 70 3 3 41 37 85 76 44 39 127 112 185 163 58 51 131 123 164 160 166
                               187 278 435 306 678 21 178 54 194 -366 -176 -205 -179 -405 -356 -445 -392
                               -41 -36 -150 -132 -244 -214 -93 -82 -193 -169 -220 -194 -28 -25 -100 -89
                               -160 -141 -61 -52 -128 -111 -149 -130 -57 -51 -54 -49 -146 -130 -47 -41 -87
                               -77 -90 -80 -32 -32 -37 -32 -130 15 -50 25 -130 56 -177 69 l-86 22 124 110
                               c68 60 205 181 304 269 100 88 286 252 415 365 129 113 259 228 290 255 97 87
                               203 181 261 231 230 199 457 404 504 457 159 174 276 423 310 656 9 60 15 126
                               13 146 l-3 37 -146 -128z"></path>
                                    <path d="M1563 8149 c13 -196 91 -423 204 -591 72 -107 148 -190 303 -326 385
                               -341 519 -458 624 -551 51 -44 61 -49 75 -38 9 8 57 49 106 92 197 172 211
                               187 186 197 -5 2 -60 48 -122 103 -62 55 -158 140 -213 189 -56 49 -117 103
                               -136 120 -19 17 -163 144 -320 281 -157 137 -290 254 -296 260 -73 67 -399
                               351 -407 354 -7 3 -8 -25 -4 -90z"></path>
                                    <path d="M1560 7168 c0 -201 92 -467 228 -663 70 -101 117 -151 265 -282 104
                               -93 120 -103 135 -92 9 8 71 61 137 120 66 58 130 114 143 125 12 10 22 21 22
                               25 0 4 -86 82 -192 175 -204 180 -450 397 -508 448 -19 17 -73 65 -120 106
                               -47 41 -86 78 -88 83 -11 23 -22 0 -22 -45z"></path>
                                    <path d="M1880 3849 l0 -480 278 3 c258 3 281 5 333 26 59 24 111 68 136 116
                               20 39 28 132 14 181 -15 56 -66 122 -113 146 -22 11 -36 24 -32 28 5 4 26 20
                               48 35 105 78 99 264 -11 355 -78 64 -115 71 -398 71 l-255 0 0 -481z m548 349
                               c57 -38 75 -69 76 -131 0 -67 -27 -109 -87 -132 -34 -13 -77 -15 -232 -13
                               l-190 3 -3 140 c-1 76 0 145 3 153 4 11 40 13 198 10 192 -3 194 -3 235 -30z
                               m-15 -408 c67 -25 102 -62 117 -122 11 -43 10 -52 -11 -95 -21 -41 -32 -51
                               -79 -70 -51 -21 -68 -22 -250 -20 l-195 2 -3 150 c-1 82 0 155 2 162 4 10 47
                               13 185 13 156 0 188 -3 234 -20z"></path>
                                    <path d="M4360 4310 c-24 -24 -26 -67 -4 -98 20 -28 83 -31 107 -4 25 28 22
                               87 -5 106 -31 22 -74 20 -98 -4z"></path>
                                    <path d="M7950 3850 l0 -480 360 0 360 0 0 85 0 85 -272 2 -273 3 -3 90 c-2
                               50 0 100 3 113 l5 22 260 0 260 0 0 80 0 80 -262 2 -263 3 0 110 0 110 270 5
                               270 5 3 83 3 82 -361 0 -360 0 0 -480z"></path>
                                    <path d="M10410 3850 l0 -480 80 0 80 0 0 480 0 480 -80 0 -80 0 0 -480z"></path>
                                    <path d="M10145 4074 l-30 -36 -54 16 c-97 29 -205 15 -295 -38 -54 -32 -86
                               -69 -122 -142 -24 -50 -28 -69 -28 -148 0 -115 24 -181 90 -248 56 -57 130
                               -87 229 -95 92 -6 126 -18 159 -52 75 -78 27 -206 -88 -232 -99 -22 -207 32
                               -226 113 -6 22 -11 23 -85 23 l-80 0 3 -34 c7 -94 91 -198 188 -234 27 -10 83
                               -21 124 -24 238 -17 403 150 360 365 -10 50 -19 64 -67 110 l-56 53 40 34 c64
                               55 87 113 87 220 1 64 -4 99 -15 120 -9 17 -23 43 -32 59 -15 28 -15 30 20 73
                               20 25 32 47 27 52 -42 37 -103 81 -110 81 -5 0 -22 -17 -39 -36z m-123 -169
                               c46 -19 95 -76 104 -120 11 -62 0 -141 -27 -179 -27 -41 -93 -76 -141 -76 -49
                               1 -112 33 -144 76 -26 34 -29 45 -29 119 0 74 3 85 28 118 53 69 135 94 209
                               62z"></path>
                                    <path d="M10957 4054 c-31 -7 -71 -21 -87 -29 -198 -102 -234 -434 -63 -585
                               72 -63 139 -85 263 -84 83 0 116 5 159 22 60 24 121 62 121 77 0 5 -21 30 -46
                               56 l-46 47 -51 -27 c-43 -22 -64 -26 -132 -26 -115 0 -182 39 -201 118 l-6 27
                               267 0 268 0 -5 93 c-10 183 -95 285 -263 316 -74 14 -105 13 -178 -5z m208
                               -157 c40 -22 83 -87 70 -107 -4 -6 -73 -10 -181 -10 -194 0 -195 1 -155 66 43
                               71 183 98 266 51z"></path>
                                    <path d="M3777 4044 c-70 -22 -133 -88 -142 -148 -8 -52 12 -121 43 -149 33
                               -30 108 -56 218 -75 162 -29 203 -56 190 -126 -12 -63 -72 -96 -177 -96 -77 0
                               -147 23 -195 64 -43 37 -48 36 -82 -8 l-23 -29 28 -30 c115 -120 401 -127 513
                               -12 29 30 34 43 38 95 6 76 -1 102 -38 144 -38 42 -86 62 -199 80 -131 21
                               -168 34 -190 63 -37 46 -27 92 27 125 29 18 51 22 116 22 70 1 89 -3 140 -28
                               l58 -29 31 31 c37 38 31 49 -52 89 -47 23 -71 27 -156 30 -69 2 -115 -2 -148
                               -13z"></path>
                                    <path d="M4927 4046 c-49 -18 -116 -63 -124 -81 -11 -29 -22 -14 -25 33 l-3
                               47 -47 3 -48 3 0 -340 0 -341 50 0 50 0 0 191 c0 232 7 266 69 330 88 90 210
                               92 297 5 55 -55 64 -101 64 -336 l0 -190 50 0 c45 0 50 2 56 26 4 15 4 118 1
                               228 -5 171 -9 210 -27 258 -23 63 -85 127 -147 153 -54 23 -169 29 -216 11z"></path>
                                    <path d="M5720 4051 c-92 -30 -179 -104 -211 -179 -79 -184 -14 -400 144 -477
                               55 -26 164 -46 213 -38 114 18 171 40 229 87 l30 25 -34 35 -34 35 -41 -28
                               c-149 -102 -368 -52 -416 93 -6 20 -10 41 -8 46 2 7 104 12 281 15 153 3 283
                               8 289 11 17 10 1 148 -22 203 -27 61 -90 123 -155 152 -38 18 -72 24 -145 26
                               -52 1 -106 -1 -120 -6z m218 -106 c20 -9 48 -26 61 -38 28 -26 60 -92 61 -124
                               l0 -23 -235 0 c-230 0 -235 0 -235 20 0 28 33 90 62 117 47 44 98 62 176 63
                               44 0 87 -6 110 -15z"></path>
                                    <path d="M6438 4043 c-52 -17 -114 -70 -129 -110 -15 -40 -10 -116 10 -153 25
                               -49 103 -85 221 -105 169 -28 210 -51 210 -114 0 -77 -76 -117 -205 -109 -75
                               5 -140 31 -186 75 l-26 24 -27 -31 c-14 -17 -26 -38 -26 -45 0 -7 17 -27 38
                               -44 113 -94 358 -106 461 -23 62 50 76 77 76 145 0 122 -62 172 -250 202 -154
                               25 -205 54 -205 118 0 32 48 73 103 88 58 15 148 3 213 -29 l53 -26 26 30 c14
                               16 25 32 25 36 0 14 -78 59 -126 74 -62 18 -196 17 -256 -3z"></path>
                                    <path d="M7141 4048 c-54 -15 -117 -63 -136 -104 -27 -57 -17 -145 21 -185 41
                               -43 97 -66 206 -84 168 -29 208 -51 208 -116 0 -77 -75 -116 -208 -106 -78 6
                               -137 30 -183 74 l-27 25 -26 -35 c-14 -18 -26 -39 -26 -45 0 -6 17 -24 38 -41
                               113 -94 358 -106 461 -23 54 44 72 74 77 133 12 122 -66 188 -253 215 -162 24
                               -230 74 -193 142 27 46 76 67 161 67 64 0 87 -5 138 -29 l60 -29 23 29 c13 15
                               23 33 23 38 0 17 -65 55 -118 71 -56 17 -193 19 -246 3z"></path>
                                    <path d="M8983 4041 c-141 -48 -218 -169 -216 -341 2 -210 119 -337 319 -348
                               72 -4 89 -1 135 20 30 14 65 37 79 51 33 35 38 34 42 -10 l3 -38 78 -3 77 -3
                               -2 338 -3 338 -75 0 -75 0 -3 -37 c-4 -43 -11 -47 -38 -17 -10 12 -41 32 -69
                               45 -63 30 -174 32 -252 5z m218 -136 c84 -30 128 -98 129 -198 0 -80 -30 -141
                               -89 -179 -42 -27 -52 -29 -118 -26 -62 3 -76 7 -110 34 -61 50 -78 87 -78 174
                               0 65 4 81 28 117 50 78 147 110 238 78z"></path>
                                    <path d="M11677 4046 c-21 -8 -56 -26 -77 -40 -105 -69 -106 -228 -3 -299 52
                               -35 77 -44 174 -57 113 -16 148 -28 161 -55 13 -30 -13 -82 -50 -96 -46 -18
                               -151 -6 -212 24 -30 14 -63 32 -73 39 -16 11 -22 9 -43 -17 -14 -16 -31 -42
                               -39 -56 -13 -25 -13 -29 12 -53 87 -87 286 -117 429 -65 193 72 190 333 -5
                               389 -20 6 -74 15 -119 21 -46 5 -97 16 -113 24 -33 17 -46 54 -30 83 26 48
                               158 55 245 13 l48 -24 39 44 c21 24 37 51 36 59 -1 8 -29 28 -61 45 -54 27
                               -69 30 -170 32 -76 2 -123 -1 -149 -11z"></path>
                                    <path d="M2814 4036 c-3 -8 -4 -113 -2 -233 5 -243 12 -280 72 -349 48 -56
                               116 -87 199 -92 88 -5 147 12 208 60 27 21 51 38 53 38 3 0 6 -19 8 -42 l3
                               -43 48 -3 47 -3 0 341 0 341 -52 -3 -53 -3 -5 -220 c-3 -121 -9 -229 -14 -240
                               -65 -141 -262 -171 -359 -55 -41 48 -47 90 -47 313 l0 207 -50 0 c-35 0 -52
                               -5 -56 -14z"></path>
                                    <path d="M4360 3710 l0 -340 50 0 50 0 0 340 0 340 -50 0 -50 0 0 -340z"></path>
                                    <path d="M4585 3098 c-25 -7 -58 -30 -91 -63 -44 -44 -54 -60 -60 -101 -31
                               -206 204 -334 364 -200 17 15 32 32 32 37 0 15 -47 10 -61 -7 -22 -27 -71 -46
                               -119 -46 -158 0 -237 192 -123 299 62 59 151 66 225 17 21 -13 47 -24 60 -24
                               l21 0 -24 26 c-53 57 -147 83 -224 62z"></path>
                                    <path d="M7125 3101 c-188 -48 -233 -273 -77 -385 40 -28 51 -31 122 -31 71 0
                               82 3 122 30 42 30 62 65 36 65 -7 0 -33 -14 -57 -30 -75 -51 -168 -42 -228 22
                               -99 105 -39 272 105 294 46 7 120 -15 142 -42 7 -7 24 -14 38 -14 l25 0 -24
                               26 c-49 53 -139 82 -204 65z"></path>
                                    <path d="M1960 2890 l0 -213 94 5 c108 6 159 26 193 76 28 43 39 128 24 199
                               -22 111 -58 135 -208 141 l-103 4 0 -212z m221 149 c36 -19 54 -69 54 -151 0
                               -62 -4 -82 -21 -105 -33 -45 -80 -64 -154 -61 l-65 3 -3 167 -2 167 47 3 c52
                               3 114 -7 144 -23z"></path>
                                    <path d="M7740 2889 c0 -164 3 -210 13 -207 9 4 13 57 15 211 2 180 0 207 -13
                               207 -13 0 -15 -29 -15 -211z"></path>
                                    <path d="M8170 2890 c0 -173 2 -210 14 -210 8 0 17 7 20 15 5 14 9 14 35 0
                               111 -57 231 50 185 164 -28 71 -119 103 -184 66 -14 -8 -28 -14 -32 -15 -5 0
                               -8 43 -8 95 0 78 -3 95 -15 95 -13 0 -15 -30 -15 -210z m179 6 c68 -36 66
                               -134 -4 -172 -80 -43 -168 46 -127 128 27 53 78 71 131 44z"></path>
                                    <path d="M2353 3039 c-16 -16 -5 -39 19 -39 14 0 19 6 16 22 -4 25 -19 33 -35
                               17z"></path>
                                    <path d="M6470 2995 c0 -34 -5 -47 -20 -55 -11 -6 -20 -15 -20 -19 0 -5 9 -11
                               20 -14 18 -5 20 -14 20 -116 0 -91 3 -111 15 -111 12 0 15 22 17 113 l3 112
                               28 3 c15 2 27 9 27 17 0 8 -12 15 -27 17 -26 3 -28 7 -31 51 -2 33 -7 47 -18
                               47 -10 0 -14 -12 -14 -45z"></path>
                                    <path d="M9040 2994 c0 -37 -4 -47 -20 -51 -11 -3 -20 -11 -20 -18 0 -7 9 -15
                               20 -18 18 -5 20 -14 20 -116 0 -92 3 -111 15 -111 12 0 15 19 15 115 l0 115
                               31 0 c38 0 35 24 -4 28 -25 3 -27 7 -27 53 0 37 -4 49 -15 49 -11 0 -15 -12
                               -15 -46z"></path>
                                    <path d="M2355 2928 c-3 -8 -4 -66 -3 -129 2 -93 6 -114 18 -114 13 0 15 20
                               15 124 0 91 -3 125 -12 128 -7 3 -15 -2 -18 -9z"></path>
                                    <path d="M2490 2920 c-40 -40 -14 -102 47 -116 65 -14 78 -21 81 -44 7 -48
                               -64 -67 -111 -30 -30 24 -51 26 -43 4 3 -9 6 -17 6 -20 0 -10 63 -34 90 -34
                               33 0 70 15 82 34 14 20 8 73 -9 89 -10 8 -39 21 -65 27 -26 7 -53 18 -59 26
                               -31 37 35 69 84 41 35 -21 47 -21 47 -3 0 44 -113 63 -150 26z"></path>
                                    <path d="M2767 2921 c-59 -38 -76 -100 -48 -168 36 -88 186 -96 231 -14 15 30
                               -9 28 -56 -4 -43 -29 -66 -31 -104 -8 -67 39 -64 140 5 171 38 18 58 15 99
                               -13 52 -35 67 -34 46 6 -26 47 -122 64 -173 30z"></path>
                                    <path d="M3055 2908 c-34 -31 -39 -41 -43 -91 -4 -55 -3 -58 36 -97 36 -36 44
                               -40 91 -40 44 0 56 5 89 34 35 33 37 38 37 95 0 55 -3 64 -32 92 -26 26 -41
                               33 -86 37 -51 4 -57 2 -92 -30z m134 -14 c13 -9 29 -32 37 -50 12 -29 12 -39
                               0 -68 -29 -68 -94 -86 -146 -38 -26 22 -31 35 -32 71 0 38 4 49 31 73 36 32
                               75 36 110 12z"></path>
                                    <path d="M3310 2928 c1 -30 99 -243 113 -246 9 -2 22 7 31 20 21 33 98 229 92
                               235 -15 15 -36 -14 -71 -96 -21 -51 -43 -90 -48 -88 -6 2 -27 43 -47 93 -23
                               57 -42 90 -53 92 -9 2 -17 -2 -17 -10z"></path>
                                    <path d="M3648 2924 c-82 -44 -78 -184 7 -230 45 -24 119 -15 153 20 23 22 24
                               26 8 32 -10 4 -24 0 -35 -10 -10 -9 -34 -19 -53 -22 -27 -5 -40 -1 -67 21 -55
                               47 -41 58 79 59 100 1 105 2 108 23 5 32 -24 81 -61 103 -38 24 -101 25 -139
                               4z m140 -49 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -89 0 -103 6 -82 36 34
                               50 110 54 150 9z"></path>
                                    <path d="M3919 2933 c-5 -26 -6 -221 -2 -235 2 -10 11 -18 19 -18 11 0 14 18
                               14 84 0 92 17 128 65 140 26 7 35 36 10 36 -8 0 -26 -6 -41 -12 -22 -10 -28
                               -10 -32 0 -4 13 -31 17 -33 5z"></path>
                                    <path d="M4943 2919 c-20 -12 -38 -37 -48 -64 -22 -55 -9 -101 41 -145 28 -24
                               43 -30 83 -30 128 0 178 158 75 239 -36 28 -108 28 -151 0z m138 -38 c23 -23
                               29 -38 29 -71 0 -33 -6 -48 -29 -71 -39 -38 -80 -39 -123 0 -27 24 -33 36 -33
                               71 0 35 6 47 33 71 43 39 84 38 123 0z"></path>
                                    <path d="M5220 2810 c0 -109 2 -130 15 -130 8 0 15 8 16 18 5 164 5 165 33
                               189 47 40 108 25 121 -30 3 -12 6 -55 7 -96 2 -49 7 -76 16 -79 9 -3 12 23 12
                               105 0 102 -2 111 -23 131 -26 24 -93 30 -129 11 -14 -8 -23 -8 -31 0 -6 6 -17
                               11 -24 11 -10 0 -13 -30 -13 -130z"></path>
                                    <path d="M5530 2810 c0 -109 2 -130 15 -130 12 0 15 15 15 74 0 93 13 131 50
                               146 35 14 85 5 96 -18 4 -9 10 -57 14 -106 3 -55 10 -91 17 -93 16 -6 19 26
                               13 130 -4 78 -8 91 -29 108 -27 22 -110 26 -129 7 -9 -9 -15 -9 -24 0 -32 32
                               -38 14 -38 -118z"></path>
                                    <path d="M5873 2918 c-72 -52 -76 -147 -8 -207 48 -42 118 -44 164 -5 17 14
                               31 30 31 35 0 16 -31 10 -57 -11 -35 -27 -75 -25 -113 6 -53 45 -39 56 80 57
                               l105 2 3 32 c3 25 -4 40 -31 69 -29 32 -41 38 -88 41 -45 4 -60 0 -86 -19z
                               m145 -43 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -102 0 -112 10 -58 55 26
                               22 39 26 67 21 21 -3 45 -16 59 -31z"></path>
                                    <path d="M6172 2906 c-35 -33 -37 -38 -37 -96 0 -58 2 -63 37 -96 32 -29 45
                               -34 88 -34 41 0 55 5 85 31 19 17 35 35 35 40 0 17 -32 9 -62 -16 -54 -45
                               -124 -22 -148 49 -19 58 17 112 82 122 27 5 40 1 65 -20 41 -34 80 -35 55 -1
                               -24 34 -67 55 -113 55 -42 0 -55 -5 -87 -34z"></path>
                                    <path d="M7468 2922 c-36 -21 -58 -69 -58 -122 0 -36 6 -48 39 -81 35 -35 44
                               -39 90 -39 42 0 56 5 83 29 42 38 28 52 -22 23 -51 -29 -81 -28 -119 4 -56 47
                               -42 59 61 54 26 -1 66 1 88 5 47 8 52 30 20 82 -36 60 -124 82 -182 45z m140
                               -47 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -102 0 -112 10 -58 55 26 22 39
                               26 67 21 21 -3 45 -16 59 -31z"></path>
                                    <path d="M7893 2918 c-39 -28 -53 -57 -53 -109 0 -78 53 -129 134 -129 36 0
                               52 6 78 29 42 38 28 52 -22 23 -21 -12 -50 -22 -64 -22 -44 0 -115 78 -73 81
                               6 0 53 2 102 4 50 1 93 3 98 4 14 2 7 46 -13 78 -38 63 -127 83 -187 41z m145
                               -43 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -102 0 -112 10 -58 55 26 22 39
                               26 67 21 21 -3 45 -16 59 -31z"></path>
                                    <path d="M8504 2927 c-2 -7 -3 -65 -2 -128 2 -93 6 -114 18 -114 12 0 16 17
                               18 80 3 95 14 121 59 137 18 7 33 18 33 25 0 16 -42 17 -58 1 -10 -10 -17 -10
                               -32 0 -25 15 -29 15 -36 -1z"></path>
                                    <path d="M8723 2918 c-103 -73 -53 -238 72 -238 20 0 49 7 66 15 26 14 30 14
                               35 0 3 -8 12 -15 20 -15 11 0 14 26 14 130 0 104 -3 130 -14 130 -8 0 -17 -7
                               -20 -15 -5 -14 -9 -14 -35 0 -43 22 -101 19 -138 -7z m117 -23 c36 -19 50 -43
                               50 -88 0 -73 -77 -118 -138 -81 -67 39 -69 127 -4 167 34 21 55 21 92 2z"></path>
                                    <path d="M9213 2907 c-32 -30 -37 -41 -41 -90 -4 -55 -3 -58 36 -97 35 -35 45
                               -40 87 -40 48 0 100 23 110 48 10 26 -5 27 -45 4 -53 -30 -85 -28 -121 7 -45
                               46 -35 59 41 52 14 -2 53 0 88 4 61 6 62 7 62 37 0 22 -11 41 -39 69 -35 35
                               -44 39 -90 39 -44 0 -56 -5 -88 -33z m146 -23 c53 -44 43 -54 -59 -54 -71 0
                               -90 3 -90 14 0 23 59 66 90 66 16 0 42 -11 59 -26z"></path>
                                    <path d="M4188 2815 c-16 -35 -1 -59 40 -63 27 -3 32 1 38 24 9 35 -6 58 -39
                               62 -21 3 -30 -3 -39 -23z"></path>
                                    <path d="M6725 2824 c-34 -35 -1 -83 48 -70 21 5 27 12 27 34 0 49 -42 69 -75
                               36z"></path>
                                </g>
                            </svg>
                        </a>
                    </div>
                    <!-- /.header-menu__logo -->
                    <div class="header-menu__nav">


                    </div>
                    <!-- /.header-menu__nav -->
                    <div class="header-menu__actions">
                        @auth
                            <a href="{{ route('login') }}" class="au">
                                <figure>
                                    <img src="{{auth()->user()->profile}}" alt="">
                                </figure>
                                Welcome , {{auth()->user()->first_name}}
                            </a>
                        @else
                            <a href="{{route('login')}}" class="login">
                                <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g>
                                        <path id="Vector" d="M18 19C18 16.7909 15.3137 15 12 15C8.68629 15 6 16.7909 6 19M12 12C9.79086 12 8 10.2091 8 8C8 5.79086 9.79086 4 12 4C14.2091 4 16 5.79086 16 8C16 10.2091 14.2091 12 12 12Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </g>
                                </svg>
                            </a>
                        @endauth
                        <a href="#" class="srch">
                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17 17L21 21" stroke="#323232" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11Z" stroke="#323232" stroke-width="2"/>
                            </svg>
                        </a>
                        <a href="#" class="show-cart">
                            <span>{{$carts->count()}}</span>
                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>

                            <div id="icon">
                                <span></span>
                                <span></span>
                                <span></span>
                                <span></span>
                                <span></span>
                                <span></span>
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                    </div>
                    @include('includes.cart')
                </div>
                <!-- /.header-menu -->
            </div>
            <!-- /.col-md-12 -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container -->
</header>


<div class="menu-popup">
    <h2>@lang("common.navigation") <a href="#" class="clo"><i class="icofont-close"></i></a>
        <!-- /.clo --></h2>

    <a href="/courses" class="all-courses__btn"><svg width="800px" height="800px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" mirror-in-rtl="true">
            <path fill="#494c4e" d="M7.857 10H2.142C.962 10 0 9.04 0 7.857V2.143C0 .96.96 0 2.142 0h5.715C9.04 0 10 .96 10 2.143v5.714C10 9.04 9.04 10 7.857 10zM2.142 2C2.066 2 2 2.067 2 2.143v5.714c0 .076.066.143.142.143h5.715C7.933 8 8 7.933 8 7.857V2.143C8 2.067 7.933 2 7.857 2H2.142zM7.857 24H2.142C.962 24 0 23.04 0 21.857v-5.715C0 14.962.96 14 2.142 14h5.715C9.04 14 10 14.96 10 16.143v5.715C10 23.038 9.04 24 7.857 24zm-5.715-8c-.076 0-.142.066-.142.143v5.715c0 .076.066.142.142.142h5.715c.076 0 .143-.066.143-.143v-5.715c0-.076-.067-.142-.143-.142H2.142zM21.857 24h-5.715C14.96 24 14 23.04 14 21.857v-5.715C14 14.96 14.96 14 16.142 14h5.715C23.04 14 24 14.96 24 16.14v5.715C24 23.04 23.037 24 21.856 24zm-5.715-8c-.076 0-.143.066-.143.143v5.715c0 .076.065.143.142.143h5.715c.076 0 .143-.065.143-.142v-5.715c0-.076-.066-.143-.143-.143h-5.715zM17.818 12.364c-.55 0-1.098-.208-1.516-.626l-4.04-4.04c-.837-.836-.837-2.196 0-3.03L16.3.625c.808-.808 2.225-.807 3.03 0l4.04 4.04c.837.835.837 2.195 0 3.03l-4.04 4.04c-.418.42-.967.628-1.514.628zm0-10.364c-.028 0-.067.007-.102.04l-4.04 4.04c-.055.055-.055.15 0 .203l4.04 4.04c.055.055.147.056.202 0l4.04-4.04c.055-.054.055-.148 0-.202l-4.04-4.04c-.034-.033-.073-.04-.1-.04z"/>
        </svg> @lang("common.all_courses")</a>
    <!-- /.all-courses__btn -->
    <ul>
        <x-shuttle-menu id="{{ setting('header_menu') }}"></x-shuttle-menu>
    </ul>
</div>
<!-- /.menu-popup -->



<div class="course-categoris " id="courses" data-id="courses">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="title-on__it">
                    <h1>    <svg fill="#000000" width="800px" height="800px" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0h16v7H0V0zm2 2v3h12V2H2zM0 9h16v7H0V9zm2 2v3h12v-3H2z" fill-rule="evenodd"/>
                        </svg> @lang("common.c_categories")</h1>
                </div>
                <!-- /.title-on__it -->
            </div>
            <!-- /.col-md-12 -->

            @foreach($categories as $cat)
                <div class="col-md-12">
                    <div class="courses-slider__item">
                        <a href="{{route('course.index')}}?category_id={{$cat->id}}">
                            <figure>
                                <img src="{{Storage::url($cat->image)}}" alt="">
                            </figure>
                            <div class="title-in">
                                <h2>{{$cat->name}}</h2>
                                <span>{{$cat->courses_count}} Festival</span>
                            </div>
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container -->
</div>
<!-- /.course-categoris -->
