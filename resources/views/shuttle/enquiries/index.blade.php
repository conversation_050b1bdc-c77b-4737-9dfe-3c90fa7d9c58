@extends('shuttle::admin')
@section('breadcrumbs')
    @php
        $breadCrumbs = [
            'Home' => route('shuttle.index'),
            $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)
        ]
    @endphp
    <div class="pageTitle">
        <div class="pageTitle-title">
            <h1>{{array_key_last ($breadCrumbs)}}</h1>
        </div>
        <!-- /.pageTitle-title -->
        <div class="pageTitle-down">
            <ul>
                @foreach($breadCrumbs as $bread => $route)
                    <li class="breadcrumb-item @if($loop->last) active @endif">
                        <a href="{{$route}}">{{$bread}}</a>
                    </li>
                @endforeach
            </ul>
            <a href="{{route('shuttle.export.enquiries')}}" class="btn btn-primary">
                <svg width="800px" height="800px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g><g><g><line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12" x2="12" y1="19" y2="5"/><line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5" x2="19" y1="12" y2="12"/></g></g></g></svg>
                Export</a>
        </div>
    </div>
@stop

@section('main')

    <div class="row mb-5">
        <div class="col-md-12">
            <div class="card">
                <form class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <select name="" id="">
                                <option value="Choose Category">Choose Category</option>
                                <option value="Choose Category"></option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <button class="btn btn-primary w-100" type="submit">Search</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <scaffold-interface-table
            url="{{ route('shuttle.scaffold_interface.datatable', $scaffoldInterface) }}"
            delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
            :columns="{{ json_encode($columns) }}"></scaffold-interface-table>

@stop
