"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[648],{632:(i,t,e)=>{e.d(t,{Z:()=>o});var n=e(3645),a=e.n(n)()((function(i){return i[1]}));a.push([i.id,"ul{list-style-type:none;margin:0;padding:0}*,:after,:before{box-sizing:border-box}input:focus{outline:none}input[disabled]{background-color:transparent}div.vue-tags-input.disabled{opacity:.5}div.vue-tags-input.disabled *{cursor:default}.ti-input{background:#fff;border:1px solid #d7d7d7;border-radius:.1rem;box-shadow:none!important;color:#3a3a3a;font-size:.8rem;line-height:1;min-height:calc(2em + .8rem);outline:initial!important;padding:.35rem .75rem}.ti-input,.ti-tags{display:flex;flex-wrap:wrap}.ti-tags{height:100%;line-height:1em;width:100%}.ti-tag{background-color:#5c6bc0;border-radius:2px;color:#fff;display:flex;font-size:.8rem;margin-bottom:1px;margin-right:2px;margin-top:1px;padding:3px 5px}.ti-tag:focus{outline:none}.ti-tag .ti-content{align-items:center;display:flex}.ti-tag .ti-tag-center{position:relative}.ti-tag span{line-height:.85em}.ti-tag span.ti-hidden{height:0;padding-left:14px;visibility:hidden;white-space:pre}.ti-tag .ti-actions{align-items:center;display:flex;font-size:1.15em;margin-left:2px}.ti-tag .ti-actions i{cursor:pointer}.ti-tag:last-child{margin-right:4px}.ti-tag.ti-invalid,.ti-tag.ti-tag.ti-deletion-mark{background-color:#e54d42}.ti-new-tag-input-wrapper{display:flex;flex:1 0 auto;font-size:.8rem;margin:2px;padding:3px 5px}.ti-new-tag-input-wrapper input{border:none;flex:1 0 auto;margin:0;min-width:100px;padding:0}.ti-new-tag-input{line-height:normal}.ti-autocomplete{background-color:#fff;border:1px solid #ccc;border-top:none;position:absolute;width:100%;z-index:20}.ti-item>div{cursor:pointer;padding:3px 6px;width:100%}.ti-selected-item{background-color:#5c6bc0;color:#fff}",""]);const o=a},8648:(i,t,e)=>{e.r(t),e.d(t,{default:()=>d});var n=e(3379),a=e.n(n),o=e(632),r={insert:"head",singleton:!1};a()(o.Z,r);const d=o.Z.locals||{}}}]);