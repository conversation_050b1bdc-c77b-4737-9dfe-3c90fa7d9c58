"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[31],{2031:(t,e,n)=>{n.r(e),n.d(e,{clone:()=>u,createClasses:()=>a,createTag:()=>c,createTags:()=>o});var r=function(t,e){return e.filter((function(e){var n=t.text;return"string"==typeof e.rule?!new RegExp(e.rule).test(n):e.rule instanceof RegExp?!e.rule.test(n):"[object Function]"==={}.toString.call(e.rule)?e.rule(t):void 0})).map((function(t){return t.classes}))},u=function(t){return JSON.parse(JSON.stringify(t))},i=function(t,e){for(var n=0;n<t.length;){if(e(t[n],n,t))return n;n++}return-1},a=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=arguments.length>3?arguments[3]:void 0;void 0===t.text&&(t={text:t});var c=r(t,n),o=i(e,(function(e){return e===t})),l=u(e),s=-1!==o?l.splice(o,1)[0]:u(t),f=a?a(l,s):-1!==l.map((function(t){return t.text})).indexOf(s.text);return f&&c.push("ti-duplicate"),0===c.length?c.push("ti-valid"):c.push("ti-invalid"),c},c=function(t){void 0===t.text&&(t={text:t});for(var e=u(t),n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return e.tiClasses=a.apply(void 0,[t].concat(r)),e},o=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return t.map((function(e){return c.apply(void 0,[e,t].concat(n))}))}}}]);