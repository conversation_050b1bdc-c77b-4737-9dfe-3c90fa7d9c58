<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Location;
use App\Models\Course;
use App\Models\Cart;
use App\Models\CourseLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class CartController extends Controller
{
    public function index(){
        $cart = $this->getCart();

        return view('cart', compact('cart'));
    }
    public function store(Request $request, CourseLocation $courseLocation)
    {
        $cookie_id = Cookie::get('business_eagles_cart') ?? uniqid('business_eagles_');

        $cartItem = Cart::query()
            ->where(function($query) use ($cookie_id) {
                $query->when(auth()->check(),fn($q) => $q->where('user_id', auth()->user()->id))
                    ->orWhere('cookie_id', $cookie_id);
            })
            ->where('course_location_id', $courseLocation->id)
            ->firstOrCreate([
                'user_id' => auth()->user()?->id,
                'cookie_id' => $cookie_id,
                'course_location_id' => $courseLocation->id,
            ]);

        $cartItem->qty = $cartItem->qty + 1;
        $cartItem->save();


        return response($this->getResponse())->withCookie(cookie()->forever(
            'business_eagles_cart', $cookie_id
        ));
    }

    public function update(Request $request, Cart $cart)
    {
        if($request->qty){
            $cart->qty = $request->qty;
            $cart->save();
        }

        $totalPrice = $this->getCartْQueryْْ()
            ->select(\DB::raw('(course_locations.price * carts.qty) as p'))
            ->join('course_locations', 'course_location_id', 'course_locations.id')
            ->get();

        return ['total' => $totalPrice->sum('p')];
//        return view('includes.cart', ['carts' =>  $this->getCart()]);
    }

    public function destroy(Request $request, Cart $cart)
    {
        $cart->delete();
    
        $remainingCarts = $this->getCart();
        $newTotal = $remainingCarts->sum(function($item) {
            return $item->qty * $item->courseLocation->price;
        });
    
        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart.',
            'new_total' => $newTotal,
            'count' => $remainingCarts->count()
        ]);
    }

    private function getResponse(){
        $carts = $this->getCart();
        return [
            'html' => view('includes.cart', [
                'carts' =>  $carts
            ])->render(),
            'count' => $carts->count(),
            'total' => $carts->sum(function($item){
                return $item->qty * $item->courseLocation->price;
            })
        ];
    }

    private function getCart(){
        return
            $this->getCartْQueryْْ()
                ->with('courseLocation.course', 'courseLocation.location')
                ->get();
    }

    private function getCartْQueryْْ(){
        $cookieId = Cookie::get('business_eagles_cart');
        $user = auth()->user();
        return
            Cart::query()
                ->where(fn($query) =>
                $query
                    ->when($user, function ($q) use ($user){
                        $q->where('user_id', $user->id);
                    })
                    ->when($cookieId, function ($q) use ($cookieId){
                        $q->orWhere('cookie_id', $cookieId);
                    })
                )->whereHas('courseLocation');
    }
}
