

<div class="row">
   
    <div class="col-md-8">
        <div class="compo">
        <div class="row sortable">
        @if($add)
        <div class="col-12">
<div class="alert-for-component">
    
<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="947.25" height="603.69" viewBox="0 0 947.25 603.69" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M452.65527,713.275l-29,16.84,28.58985-6.9c-.22022,2.36-.52,4.68-.91992,6.95-.11036.63-.23,1.25-.36036,1.87a51.87781,51.87781,0,0,1-5.25,14.92005c-.48.86-.96,1.69995-1.46,2.51-.48.82-.98,1.61-1.48974,2.38H365.895c-.64014-.77-1.25-1.57-1.81006-2.38a35.97564,35.97564,0,0,1-6.17969-26.62,44.96785,44.96785,0,0,1,1.19971-5.63,51.56839,51.56839,0,0,1,1.83008-5.57,73.442,73.442,0,0,1,4.41015-9.35c3.52-6.34,9.00977-12.06,15.48-17.14a108.06933,108.06933,0,0,1,9.77978-6.8q.96021-.615,1.9502-1.2c1.00976-.6,2.02-1.19,3.04-1.76,2.02979-1.16,4.07959-2.26,6.12989-3.3.54-.28,1.06982-.55,1.6001-.81,4.4497-2.22,8.87988-4.21,13.0996-5.96.53028-.22,1.07032-.44,1.6001-.65a233.30757,233.30757,0,0,1,25.72022-8.81s.02978.08.08008.25c.12988.46.46,1.57.90966,3.21,1.25,4.55,3.4502,13.23,5.22022,23.71.1001.57.18994,1.15.29,1.73005a181.68776,181.68776,0,0,1,2.4497,26.81c.01026.56.01026,1.11.01026,1.67Z" transform="translate(-126.375 -148.155)" fill="#f2f2f2"/><path d="M445.86523,654.845c-.01025.03-.11035.52-.34033,1.42-.18994.75-.4497,1.78-.79,3.06-1.16992,4.47-3.25,11.96-6.21,20.98-.16992.52-.3501,1.06-.52978,1.6-2.06006,6.17-4.51026,12.99-7.33985,20-.71045,1.76-1.42041,3.49-2.15039,5.18-.21972.53-.43994,1.05005-.66992,1.56q-4.65015,10.77007-9.56982,19.71l-.83008,1.49a134.52429,134.52429,0,0,1-13.18994,19.62c-.66992.82-1.3501,1.61-2.04,2.38h-2.32031c.70019-.77,1.40039-1.57,2.08007-2.38a132.14581,132.14581,0,0,0,13.87012-20.4c.28027-.49.56006-.98.83008-1.48,3.52978-6.41,6.74023-13.11,9.60986-19.75.23-.51.4502-1.03.66992-1.56,3.75-8.78,6.90039-17.41,9.44-25.00994.18017-.54.36035-1.08.53027-1.61005,3.70019-11.22,6.02-20.02,6.91016-23.54.11962-.52.21972-.93.29-1.21.06983-.26.09961-.41.10987-.44l.81006.19h.00976Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M451.48486,683.165c-.50976-.03-1.02-.08-1.52978-.13a44.99648,44.99648,0,0,1-11.43018-2.73c-.52978-.21-1.06982-.42-1.60986-.65a45.858,45.858,0,0,1-4.28027-2.11,45.27611,45.27611,0,0,1-14.60987-12.87c-.12988-.17-.25976-.35-.37988-.52q-.78.33-1.58984.66c.11962.17.23974.34.36962.51a46.76551,46.76551,0,0,0,15.39014,13.7,48.307,48.307,0,0,0,4.56983,2.24006c.53027.22,1.07031.44,1.61035.63995a46.89079,46.89079,0,0,0,12.25,2.86005c.5.05,1.00976.09,1.50976.12C451.665,684.295,451.5752,683.725,451.48486,683.165Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M452.69482,711.575a45.37037,45.37037,0,0,1-24.18994-4.49q-.79467-.375-1.56006-.81a4.2326,4.2326,0,0,1-.46972-.25,45.58023,45.58023,0,0,1-23.1499-34.74c-.02-.17-.04-.35-.06006-.51995-.54.28-1.08008.55-1.60987.83.01953.17.03955.33.06983.5a47.22571,47.22571,0,0,0,23.93017,35.41c.19971.11.40967.23.61963.32995q.78.42,1.56006.81a47.05043,47.05043,0,0,0,24.82031,4.63c.44971-.04.89991-.09,1.34961-.13995,0-.57.01026-1.13.01026-1.7C453.5752,711.485,453.13477,711.535,452.69482,711.575Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M451.3252,730.165a45.54776,45.54776,0,0,1-27.75.21,44.468,44.468,0,0,1-5.31006-2.02c-.54-.24-1.07032-.5-1.6001-.77-.56983-.28-1.12988-.57995-1.68018-.87994a45.67468,45.67468,0,0,1-23.38965-42.39,43.76337,43.76337,0,0,1,.96-7.16c.02978-.16.06-.32.10009-.48-.66015.41-1.32031.82-1.97021,1.24-.02979.15-.06006.29-.08008.44a46.77057,46.77057,0,0,0-.73,9.79,47.25281,47.25281,0,0,0,24.28027,40.03c.55957.31,1.11963.61,1.67969.89.53027.27,1.07031.53,1.6001.78a47.17478,47.17478,0,0,0,33.52978,2.19c.37012-.1.74024-.22,1.11036-.34.1499-.64.27978-1.28.3999-1.92C452.09521,729.915,451.71484,730.045,451.3252,730.165Z" transform="translate(-126.375 -148.155)" fill="#fff"/><rect x="200.25" y="246.52002" width="56" height="56" fill="#9e616a"/><polygon points="165.326 581.214 176.36 581.213 181.608 538.656 165.324 538.657 165.326 581.214" fill="#9e616a"/><path d="M321.98486,741.805l-16.57959-11.45v-7.47l-1.88037.11-11.81982.71-3.74024.22-2.12988,25.54-.06982.85h7.83007l.15967-.85,1.24024-6.54,3.16992,6.54.41016.85H319.335a4.67905,4.67905,0,0,0,4.66992-4.66A4.67076,4.67076,0,0,0,321.98486,741.805Z" transform="translate(-126.375 -148.155)" fill="#2f2e41"/><polygon points="229.853 574.209 240.457 577.258 257.265 537.81 241.615 533.309 229.853 574.209" fill="#9e616a"/><path d="M381.895,742.695l-12.77-15.59,2.06982-7.18-1.83984-.41-10.25-2.3-1.31006-.29-3.6499-.82-9.41016,24.75,7.52,2.17,3.39014-6.72,1.39014,8.09,4.73974,1.36005,12.89014,3.71,2.33008.67a4.66278,4.66278,0,0,0,4.8999-7.44Z" transform="translate(-126.375 -148.155)" fill="#2f2e41"/><path d="M1070.355,148.155H485.895a3.28,3.28,0,0,0-3.27,3.28v19.25a3.27992,3.27992,0,0,0,3.27,3.28h584.46a3.27961,3.27961,0,0,0,3.27-3.28v-19.25A3.27966,3.27966,0,0,0,1070.355,148.155Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><circle id="b5b16cf8-67a2-4f26-b1bf-3e43b6764a92" data-name="Ellipse 90" cx="375.2487" cy="11.96482" r="4.64784" fill="#fff"/><circle id="b66c556c-a3ce-485d-a169-682ae89102b6" data-name="Ellipse 91" cx="392.89038" cy="11.96482" r="4.64784" fill="#fff"/><circle id="b9ccba63-6bb8-47f6-85e8-d2b1a1d873e2" data-name="Ellipse 92" cx="410.53288" cy="11.96482" r="4.64784" fill="#fff"/><path d="M1053.06494,153.915h-20.31006a1.505,1.505,0,1,0,0,3.01h20.31006a1.505,1.505,0,1,0,0-3.01Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M1053.06494,159.555h-20.31006a1.505,1.505,0,1,0,0,3.01h20.31006a1.505,1.505,0,1,0,0-3.01Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M1053.06494,165.195h-20.31006a1.505,1.505,0,1,0,0,3.01h20.31006a1.505,1.505,0,1,0,0-3.01Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M1050.75488,229.645H899.3252a3.99016,3.99016,0,0,0-3.98,3.98v61.45a3.9901,3.9901,0,0,0,3.98,3.98h151.42968a3.9838,3.9838,0,0,0,3.98-3.98v-61.45A3.98385,3.98385,0,0,0,1050.75488,229.645Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M1036.335,318.975H913.74512a4.775,4.775,0,0,0,0,9.55H1036.335C1042.55518,328.615,1042.49512,318.885,1036.335,318.975Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M1036.335,340.865H913.74512a4.775,4.775,0,0,0,0,9.55H1036.335C1042.55518,350.505,1042.49512,340.775,1036.335,340.865Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M1036.335,362.755H913.74512a4.775,4.775,0,0,0,0,9.55H1036.335C1042.55518,372.395,1042.49512,362.665,1036.335,362.755Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M851.75488,229.645H700.3252a3.99016,3.99016,0,0,0-3.98,3.98v61.45a3.9901,3.9901,0,0,0,3.98,3.98H851.75488a3.9838,3.9838,0,0,0,3.98-3.98v-61.45A3.98385,3.98385,0,0,0,851.75488,229.645Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M837.335,318.975H714.74512a4.775,4.775,0,0,0,0,9.55H837.335C843.55518,328.615,843.49512,318.885,837.335,318.975Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M837.335,340.865H714.74512a4.775,4.775,0,0,0,0,9.55H837.335C843.55518,350.505,843.49512,340.775,837.335,340.865Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M837.335,362.755H714.74512a4.775,4.775,0,0,0,0,9.55H837.335C843.55518,372.395,843.49512,362.665,837.335,362.755Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M472.04287,345.86227a10.02421,10.02421,0,0,0-.42317,1.74747l-62.00428-10.95961-53.4076-28.7994a15.08,15.08,0,0,0-16.0966,25.41817l.00006,0A118.971,118.971,0,0,0,372.847,350.26435l38.71983,12.96946,67.82117-4.34723a9.9971,9.9971,0,1,0-7.34512-13.02431Z" transform="translate(-126.375 -148.155)" fill="#9e616a"/><path d="M624.55518,346.055h-193.02a5.08706,5.08706,0,0,0-5.08008,5.08v78.31a5.087,5.087,0,0,0,5.08008,5.07995h193.02A5.08488,5.08488,0,0,0,629.625,429.445v-78.31A5.0849,5.0849,0,0,0,624.55518,346.055Z" transform="translate(-126.375 -148.155)" fill="#38cab3"/><path d="M606.1748,459.915H449.915a6.09,6.09,0,0,0,0,12.18H606.1748C614.105,472.20505,614.0249,459.805,606.1748,459.915Z" transform="translate(-126.375 -148.155)" fill="#38cab3"/><path d="M606.1748,487.815H449.915a6.09,6.09,0,0,0,0,12.18H606.1748C614.105,500.105,614.0249,487.70505,606.1748,487.815Z" transform="translate(-126.375 -148.155)" fill="#38cab3"/><path d="M606.1748,515.725H449.915a6.085,6.085,0,1,0,0,12.17H606.1748C614.105,528.005,614.0249,515.615,606.1748,515.725Z" transform="translate(-126.375 -148.155)" fill="#38cab3"/><circle cx="357.65385" cy="265.79258" r="29.72868" transform="translate(-173.49417 303.97269) rotate(-61.33683)" fill="#9e616a"/><path d="M362.625,229.675H343.4209c-12.58985,0-22.7959,11.48193-22.7959,25.64563-9.5,9.53894-6.64893,21.48919.88867,34.35437a149.22541,149.22541,0,0,1,39.541,10.57574l1.79248-7.65448,3.53564,10.08081q4.71679,2.28534,9.35352,4.99793c-3.60791-16.95111-5.09278-33.86286-2.61572-47H379.625v-7.1181l2.208,7.1181h11.792V260.675A30.9998,30.9998,0,0,0,362.625,229.675Z" transform="translate(-126.375 -148.155)" fill="#2f2e41"/><path d="M360.625,303.675,324.66978,302.116l-.967,3.67373a213.66873,213.66873,0,0,0-3.59909,92.56989l0,0h0a8.24249,8.24249,0,0,1,2.34458,8.00651l-.5165,2.0591.04779.07542a12.06182,12.06182,0,0,1,1.60133,9.00482v0l1.79834,9.90181L383.625,401.675l13-45Z" transform="translate(-126.375 -148.155)" fill="#cacaca"/><path d="M449.73154,398.51889a10.024,10.024,0,0,0-1.08438,1.43417L396.1439,365.196l-37.51032-47.69436a15.08,15.08,0,0,0-24.89132,16.89988l0,0a118.97085,118.97085,0,0,0,23.25336,28.63132l30.34607,27.323,63.93732,23.03539a9.9971,9.9971,0,1,0-1.54751-14.87242Z" transform="translate(-126.375 -148.155)" fill="#9e616a"/><path d="M563.90846,298.81988a28.73541,28.73541,0,1,1,28.73541-28.73541A28.73543,28.73543,0,0,1,563.90846,298.81988Z" transform="translate(-126.375 -148.155)" fill="#e4e4e4"/><path d="M575.24669,267.25h-8.50362v-8.50373a2.83461,2.83461,0,0,0-5.66922,0V267.25h-8.50374a2.83457,2.83457,0,0,0,0,5.66913h8.50374v8.50372a2.83461,2.83461,0,0,0,5.66922,0v-8.50372h8.50362a2.83457,2.83457,0,1,0,0-5.66913Z" transform="translate(-126.375 -148.155)" fill="#fff"/><path d="M420.61426,619.61374a16.87393,16.87393,0,0,0-1.57031-15.08753L393.625,472.675l-10-50h-60s-37.5,41.5-29.5,94.5l-2.23926,74.64014a27.44788,27.44788,0,0,0-1.50342,16.18585l.89112,4.22223-.34375.63592a21.33414,21.33414,0,0,0-.26856,19.77216L288.125,717.175l18-1,26.5-98.5,17.5-88.5,29.5,68.5,1,16-.48535,14.10333L357.125,710.175l19.5,7.5Z" transform="translate(-126.375 -148.155)" fill="#2f2e41"/><path d="M566.05518,750.655a1.193,1.193,0,0,1-1.18995,1.19H127.56494a1.19,1.19,0,0,1,0-2.38H564.86523A1.18664,1.18664,0,0,1,566.05518,750.655Z" transform="translate(-126.375 -148.155)" fill="#cacaca"/></svg>
</div>
<!-- /.alert-for-component -->        </div>
        @else

        @if($page->components->count()) 
        
       
        @foreach($page->components as $component)
        <div class="col-md-12 col-lg-6 col-sm-6 col-12 mt-2" id="{{$component->pivot->id}}">
      
<div class="compo-item">
    <figure>
        <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_429_11113)">
            <path d="M12 2.99994L15 5.99994L12 8.99994L9 5.99994L12 2.99994Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"/>
            <path d="M12 14.9999L15 17.9999L12 20.9999L9 17.9999L12 14.9999Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"/>
            <path d="M18 8.99994L21 11.9999L18 14.9999L15 11.9999L18 8.99994Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"/>
            <path d="M6 8.99994L9 11.9999L6 14.9999L3 11.9999L6 8.99994Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"/>
            </g>
            <defs>
            <clipPath id="clip0_429_11113">
            <rect width="24" height="24" fill="white"/>
            </clipPath>
            </defs>
            </svg>
    </figure>
    <div class="compo-item__title">
        <h3>{{$component->display_name}}</h3>
        <div class="btns">
        <a href="{{route('shuttle.user_component',$component->pivot->id)}}" class="btn-primary">
        <svg xmlns="http://www.w3.org/2000/svg" width="800" height="800" viewBox="0 0 24 24"><g fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M20 16v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h4"/><path d="M12.5 15.8 22 6.2 17.8 2l-9.5 9.5L8 16l4.5-.2z"/></g></svg>
        </a>
        <a href="{{route('shuttle.user_component.delete',['page_component' => $component->pivot->id])}}" class="btn-danger remove-component">
        <svg xmlns="http://www.w3.org/2000/svg" width="800" height="800" fill="none" viewBox="0 0 24 24"><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7h16M6 7v11c0 1.6569 1.3431 3 3 3h6c1.6569 0 3-1.3431 3-3V7M9 5c0-1.1046.8954-2 2-2h2c1.1046 0 2 .8954 2 2v2H9V5Z"/></svg>
        </a> 
            </div>
    </div>
</div> 
        </div>
        @endforeach

        @else

        <div class="alert-for-component">
            
<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="897.31838" height="556.97524" viewBox="0 0 897.31838 556.97524" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M368.67963,673.55906l.99774-22.43416a72.45559,72.45559,0,0,1,33.79563-8.555c-16.23146,13.27042-14.203,38.85123-25.20757,56.69681a43.58213,43.58213,0,0,1-31.95921,20.13989l-13.58307,8.31648A73.02986,73.02986,0,0,1,348.116,668.54428a70.54231,70.54231,0,0,1,12.96441-12.04606C364.33357,665.07618,368.67963,673.55906,368.67963,673.55906Z" transform="translate(-151.34081 -171.51238)" fill="#f2f2f2"/><path d="M948.26231,208.06486H315.939a1.01559,1.01559,0,0,1,0-2.03069H948.26231a1.01559,1.01559,0,0,1,0,2.03069Z" transform="translate(-151.34081 -171.51238)" fill="#cacaca"/><ellipse cx="186.95324" cy="11.16881" rx="10.92534" ry="11.16881" fill="#3f3d56"/><ellipse cx="224.69531" cy="11.16881" rx="10.92534" ry="11.16881" fill="#3f3d56"/><ellipse cx="262.43738" cy="11.16881" rx="10.92534" ry="11.16881" fill="#3f3d56"/><path d="M925.64529,174.28068h-26.81a2.0304,2.0304,0,0,0,0,4.06h26.81a2.0304,2.0304,0,0,0,0-4.06Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/><path d="M925.64529,181.90068h-26.81a2.0304,2.0304,0,0,0,0,4.06h26.81a2.0304,2.0304,0,0,0,0-4.06Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/><path d="M925.64529,189.51066h-26.81a2.0304,2.0304,0,0,0,0,4.06h26.81a2.0304,2.0304,0,0,0,0-4.06Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/><path d="M808.05408,287.65387h-434.01a8.07034,8.07034,0,0,0-8.06995,8.06v204.87a8.07888,8.07888,0,0,0,8.06995,8.07h434.01a8.07677,8.07677,0,0,0,8.06-8.07v-204.87A8.06821,8.06821,0,0,0,808.05408,287.65387Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/><path d="M693.41412,386.35389a8.06825,8.06825,0,0,0-8.06005,8.06v57.87a8.07687,8.07687,0,0,0,8.06005,8.07H816.11407v-74Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><path d="M1022.42853,460.3497H693.4184a8.07557,8.07557,0,0,1-8.06636-8.06636V394.41576a8.07532,8.07532,0,0,1,8.06636-8.06606h329.01013a8.07532,8.07532,0,0,1,8.06636,8.06606v57.86758A8.07557,8.07557,0,0,1,1022.42853,460.3497Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3" opacity="0.5"/><circle cx="586.57108" cy="255.53675" r="13.08937" fill="#fff"/><path d="M1012.23456,423.2459H775.72046a3.89775,3.89775,0,1,1,0-7.7955h236.5141a3.89775,3.89775,0,1,1,0,7.7955Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><path d="M922.40413,438.64785H775.72046a3.89794,3.89794,0,1,1,0-7.79588H922.40413a3.89794,3.89794,0,0,1,0,7.79588Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><polygon points="151.406 545.537 162.734 545.536 168.123 501.843 151.404 501.843 151.406 545.537" fill="#ffb6b6"/><path d="M299.85756,713.35055l3.18849-.00013,12.44833-5.06245,6.67193,5.06168h.0009a14.21764,14.21764,0,0,1,14.21688,14.21665v.462l-36.52585.00136Z" transform="translate(-151.34081 -171.51238)" fill="#2f2e41"/><polygon points="49.051 530.809 59.19 535.862 83.504 499.161 68.541 491.703 49.051 530.809" fill="#ffb6b6"/><path d="M199.4558,697.72216l2.8537,1.42225,13.39941,1.02234,3.71328,7.50645.00081.00041a14.21765,14.21765,0,0,1,6.3819,19.06579l-.2061.41347L192.90811,710.86Z" transform="translate(-151.34081 -171.51238)" fill="#2f2e41"/><path d="M307.766,518.60885l-72.026,1.87894,1.25263,35.07352s-1.25263,9.3947,1.25262,11.89995,3.75788,2.50525,2.50525,6.88944-4.491,46.27371-4.491,46.27371-29.56151,52.26983-28.30888,53.52246,2.50525,0,1.25263,3.13156-2.50526,1.87894-1.25263,3.13156a46.12822,46.12822,0,0,1,3.13157,3.75788h20.41554s1.14166-6.26313,1.14166-6.88944,1.25263-4.38419,1.25263-5.0105,35.66888-38.4175,35.66888-38.4175l7.51575-62.63129,18.16308,61.37867s0,53.86291,1.25263,55.11554,1.25262.62631.62631,3.13156-3.13157,1.87894-1.25263,3.75788,2.50526-1.25262,1.87894,1.87894l-.62631,3.13157,24.06246.26877s2.50525-5.27928,1.25262-7.15822-1.17747-1.366.35074-4.4409,2.15451-3.70117,1.5282-4.32748-.62631-3.95761-.62631-3.95761-9.03095-123.18392-9.03095-125.06286a6.24709,6.24709,0,0,1,.52029-2.81763V549.567l-2.39923-9.03716Z" transform="translate(-151.34081 -171.51238)" fill="#2f2e41"/><path d="M1021.02122,409.86014a27.638,27.638,0,1,1,27.638-27.638A27.638,27.638,0,0,1,1021.02122,409.86014Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><path d="M1031.92643,379.49592h-8.17886v-8.179a2.72635,2.72635,0,0,0-5.4527,0v8.179h-8.179a2.72631,2.72631,0,1,0,0,5.45261h8.179v8.179a2.72635,2.72635,0,0,0,5.4527,0v-8.179h8.17886a2.7263,2.7263,0,1,0,0-5.45261Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><path d="M599.22412,460.72388h-105.01a8.07889,8.07889,0,0,0-8.07,8.07v39.86h121.14v-39.86A8.07677,8.07677,0,0,0,599.22412,460.72388Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><path d="M599.2206,572.72388H494.21046a8.07558,8.07558,0,0,1-8.06636-8.06636V468.78994a8.07532,8.07532,0,0,1,8.06636-8.06606H599.2206a8.07532,8.07532,0,0,1,8.06636,8.06606v95.86758A8.07557,8.07557,0,0,1,599.2206,572.72388Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3" opacity="0.5"/><circle cx="373.80837" cy="321.56284" r="13.08937" fill="#fff"/><path d="M577.47185,526.05963h-61.5141a3.89775,3.89775,0,1,1,0-7.7955h61.5141a3.89775,3.89775,0,1,1,0,7.7955Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><path d="M545.64142,541.46158H515.95775a3.89794,3.89794,0,0,1,0-7.79588h29.68367a3.89794,3.89794,0,0,1,0,7.79588Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><path d="M492.02122,600.86014a27.638,27.638,0,1,1,27.638-27.638A27.638,27.638,0,0,1,492.02122,600.86014Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><path d="M502.92643,570.49592h-8.17886v-8.179a2.72635,2.72635,0,1,0-5.4527,0v8.179h-8.179a2.72631,2.72631,0,1,0,0,5.45261h8.179v8.179a2.72635,2.72635,0,1,0,5.4527,0v-8.179h8.17886a2.7263,2.7263,0,1,0,0-5.45261Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><path d="M479.22775,399.77883H374.21761a8.07557,8.07557,0,0,1-8.06636-8.06636V295.84489a8.07532,8.07532,0,0,1,8.06636-8.06606H479.22775a8.07532,8.07532,0,0,1,8.06636,8.06606v95.86758A8.07558,8.07558,0,0,1,479.22775,399.77883Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><circle cx="253.81624" cy="156.61796" r="13.08937" fill="#38cab3"/><path d="M457.47973,357.11475h-61.5141a3.89775,3.89775,0,1,1,0-7.7955h61.5141a3.89775,3.89775,0,1,1,0,7.7955Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><path d="M425.64929,372.5167H395.96563a3.89794,3.89794,0,1,1,0-7.79588h29.68366a3.89794,3.89794,0,1,1,0,7.79588Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3"/><path d="M479.22775,399.77883H374.21761a8.07557,8.07557,0,0,1-8.06636-8.06636V295.84489a8.07532,8.07532,0,0,1,8.06636-8.06606H479.22775a8.07532,8.07532,0,0,1,8.06636,8.06606v95.86758A8.07558,8.07558,0,0,1,479.22775,399.77883Z" transform="translate(-151.34081 -171.51238)" fill="#38cab3" opacity="0.5"/><circle cx="253.81624" cy="156.61796" r="13.08937" fill="#fff"/><path d="M457.47973,357.11475h-61.5141a3.89775,3.89775,0,1,1,0-7.7955h61.5141a3.89775,3.89775,0,1,1,0,7.7955Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><path d="M425.64929,372.5167H395.96563a3.89794,3.89794,0,1,1,0-7.79588h29.68366a3.89794,3.89794,0,1,1,0,7.79588Z" transform="translate(-151.34081 -171.51238)" fill="#fff"/><circle cx="225.04328" cy="115.95149" r="21" fill="#ff6584"/><path d="M434.01086,727.29762a1.18647,1.18647,0,0,1-1.19007,1.19h-280.29a1.19,1.19,0,0,1,0-2.38h280.29A1.1865,1.1865,0,0,1,434.01086,727.29762Z" transform="translate(-151.34081 -171.51238)" fill="#ccc"/><path d="M371.89562,343.08843a9.77074,9.77074,0,0,1-5.75867,12.43515,9.59965,9.59965,0,0,1-1.6355.4508l-5.54616,33.9594-13.01011-12.01254,7.2613-30.40677a9.80568,9.80568,0,0,1,8.58971-10.75944,9.54738,9.54738,0,0,1,10.09939,6.33335Z" transform="translate(-151.34081 -171.51238)" fill="#ffb6b6"/><path d="M275.88113,420.03658s10.09745-13.34141,46.7389-12.976l20.79776-7.55641,4.75311-43.57011,16.63586,3.96092-2.37655,53.8685-35.64828,20.59678-46.73885,9.50621Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/><circle id="a9729a65-ee7a-4a83-8b52-5849077a5ed0" data-name="ab6171fa-7d69-4734-b81c-8dff60f9761b" cx="119.17533" cy="198.98271" r="21.74749" fill="#ffb6b6"/><path d="M233.70808,535.39a.41692.41692,0,0,1-.11411-.01547c-.4015-.11217-.71869-.20037.7303-12.72948l1.56387-9.90346L234.3614,503.997l-2.568-2.568,4.12725-4.12686,3.46346-9.83821-5.9928-8.88023-6.87553-36.3163a28.972,28.972,0,0,1,15.9106-31.47847l7.95773-2.325,2.89542-5.30994a9.51973,9.51973,0,0,1,8.28668-4.962l14.57262-.10908a9.51971,9.51971,0,0,1,7.61675,3.7151l5.084,6.609,21.08261,7.16176-3.49559,75.3212,0,0a5.23278,5.23278,0,0,1,.35914,7.69535c-.21921.22108-.3934.40127-.50072.52007-.35548.50479.31138,4.27462,1.1349,7.47546l1.056,4.90134h0a3.01324,3.01324,0,0,0-.54773,4.39846l1.34649,1.5905v0a7.59918,7.59918,0,0,1-6.50745,8.53505C283.49975,528.62839,233.80879,535.39,233.70808,535.39Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/><path d="M264.9527,391.1777q-.13937-.30683-.27795-.61578c.03561.00114.07086.00626.10647.00719Z" transform="translate(-151.34081 -171.51238)" fill="#2f2e41"/><path d="M248.16387,349.73657a6.053,6.053,0,0,1,3.79179-1.6394c1.40626.04532,2.83233,1.31545,2.541,2.692a22.34831,22.34831,0,0,1,26.89477-10.08482c3.49521,1.23313,6.9228,3.70014,7.72569,7.31847a6.59115,6.59115,0,0,0,.83032,2.70155,3.084,3.084,0,0,0,3.28228.83155l.0345-.01017a1.02753,1.02753,0,0,1,1.24158,1.45051l-.98895,1.84442a7.92529,7.92529,0,0,0,3.77572-.0803,1.02666,1.02666,0,0,1,1.0902,1.59765,17.895,17.895,0,0,1-14.26862,7.33428c-3.9514-.0241-7.94351-1.38594-11.78913-.47726a10.24049,10.24049,0,0,0-6.88767,14.37555c-1.18139-1.2922-3.46514-.98626-4.67362.28071a6.41,6.41,0,0,0-1.3995,4.90462,22.75668,22.75668,0,0,0,2.33628,7.63859,22.83575,22.83575,0,0,1-13.53662-40.67793Z" transform="translate(-151.34081 -171.51238)" fill="#2f2e41"/><path d="M242.18035,566.5806a9.77072,9.77072,0,0,1-2.30232-13.50905,9.59969,9.59969,0,0,1,1.09205-1.29827l-14.67524-31.12295,17.52685,2.52456,11.24882,29.16783a9.80568,9.80568,0,0,1-.97986,13.73275,9.54737,9.54737,0,0,1-11.91024.50515Z" transform="translate(-151.34081 -171.51238)" fill="#ffb6b6"/><path d="M237.73617,549.58654,214.38409,497.1033l-.23387-41.45184,7.36109-22.39047a23.92467,23.92467,0,0,1,30.82742-15.04008l.16208.05839.068.15844c.27206.635,6.44593,15.90631-11.86713,47.3222l-3.68552,21.49645,12.93216,49.27378Z" transform="translate(-151.34081 -171.51238)" fill="#3f3d56"/></svg>
        </div>
        <!-- /.alert-for-component -->
        @endif @endif
    </div>
</div>
<!-- /.compo -->
</div>
    <!-- /.col-md-9 -->
    <div class="col-md-4">
        <div class="addPage">
            <form
            action="{{ ($add) ? route('shuttle.page.store',['lang' => $lang]) : route('shuttle.page.update',['page' => $page->id, 'lang' => $lang])}}"
            method="post">
            @csrf
            @if(!$add)
            @method('put')
            @endif
            <div class="row">
                <div class="form-group col-md-12">
                    <label for="page-title-input">Page Title</label>
                    <input id="page-title-input" class="form-control" name="{{$lang}}[title]"
                        placeholder="Page Title"
                        value="{{optional($page->translate($lang))->title}}">
                </div>
                <div class="form-group col-md-12">
                    <label for="page-type-input">Page Type</label>
                    <select id="page-type-input" class="form-control select2" name="type_id">
                        <option value="0">Choose Type</option>
                        @foreach($types as $type)
                        <option value="{{$type->id}}" @if($page->type_id == $type->id) selected
                            @endif>{{$type->display_name}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group col-md-12">
                    <label for="page-keywords-input">KeyWords (Seo)</label>
                    <input id="page-keywords-input" class="form-control" name="{{$lang}}[keywords]"
                        placeholder="Google KeyWords"
                        value="{{optional($page->translate($lang))->keywords}}">
                </div>
                <div class="form-group col-md-12">
                    <label for="page-description-input">Meta Description  (Seo)</label>
                    <input id="page-description-input" class="form-control" name="{{$lang}}[description]"
                        placeholder="Meta Description"
                        value="{{optional($page->translate($lang))->description}}">
                </div>
                <div class="form-group col-md-12">
                    <label>Og:image</label>
                    <image-input name="image" @if(isset($page->image))
                        path="{{ $page->image }}" preview="{{ Storage::url($page->image) }}"
                        @endif>
                    </image-input>
                </div>
            </div>
            
                    <button type="submit" class="btn btn-primary btn-fixed">Save</button>
         
        </form>
        </div>
        <!-- /.addPage -->
    </div>
    <!-- /.col-md-3 -->
</div>
<!-- /.row -->




   
</div>
<media-library-modal upload-url="{{route('shuttle.media.upload')}}"></media-library-modal>

@push('js')
<script src="{{route('shuttle.assets','js/vendor/jquery-ui.min.js')}}"></script>
<script>
    $( ".sortable" ).sortable({
            revert: true,
            update: function (event, ui) {
                let data = $(event.target).sortable("toArray");
                sortComponent(data);
            }
        });

        $("a.remove-component").on('click',function (e) {
            e.preventDefault();
            let form = $('<form action="'+$(this).attr('href')+'" method="post">'+
                '@csrf'+'@method("DELETE")'+'</form>');
            form.appendTo('body');
            form.submit();
        });

        function sortComponent(data) {
            let frmData = new FormData();
            frmData.append('data',data);
            $.ajax({
                type: "post",
                url: "{{route('shuttle.component.sort')}}",
                data: {data: data},
                success: function () {
                    showNotification('top', 'right', "success", "Saved");
                }
            });
        }
</script>
@endpush