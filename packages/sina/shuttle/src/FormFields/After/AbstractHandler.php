<?php

namespace Sina\Shuttle\FormFields\After;

use Sina\Shuttle\Http\Traits\Renderable;

abstract class <PERSON>bstractHandler implements HandlerInterface
{
    use Renderable;

    public function visible($row, $dataType, $dataTypeContent, $options)
    {
        return true;
    }

    public function handle($row, $dataType, $dataTypeContent)
    {
        $content = $this->createContent(
            $row,
            $dataType,
            $dataTypeContent,
            $row->details
        );

        return $this->render($content);
    }

    public function getCodename()
    {
        return $this->codename;
    }
}
