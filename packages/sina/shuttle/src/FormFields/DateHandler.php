<?php

namespace Sina\Shuttle\FormFields;

class Date<PERSON><PERSON>ler extends <PERSON>bstract<PERSON><PERSON><PERSON>
{
    protected $codename = 'date';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.date', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
