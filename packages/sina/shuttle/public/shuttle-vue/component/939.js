"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[939],{7939:(t,e,a)=>{a.r(e),a.d(e,{default:()=>r});var n=a(8067);const o={props:{name:{type:String,default:""},value:{type:String,default:""}},components:{ckeditor:a.n(n)().component},data:function(){var t;return{editorData:null!==(t=this.value)&&void 0!==t?t:"",editorConfig:{}}}};const r=(0,a(1900).Z)(o,(function(){var t=this,e=t._self._c;return e("div",[e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.editorData,expression:"editorData"}],attrs:{name:t.name,hidden:""},domProps:{value:t.editorData},on:{input:function(e){e.target.composing||(t.editorData=e.target.value)}}}),t._v(" "),e("ckeditor",{attrs:{config:t.editorConfig},model:{value:t.editorData,callback:function(e){t.editorData=e},expression:"editorData"}})],1)}),[],!1,null,null,null).exports}}]);