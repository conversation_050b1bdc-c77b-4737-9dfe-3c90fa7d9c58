-- Add lecturers relationship to awards
INSERT INTO shuttle_scaffold_interface_rows (id, rowable_type, rowable_id, parent_id, field, type, display_name, required, browse, read, edit, add, delete, details, ord, last_upd) VALUES 
(538, 'Sina\\Shuttle\\Models\\ScaffoldInterface', 16, 0, 'awward_belongstomany_lecturer_relationship', 'relationship', 'lecturers', 0, 0, 1, 1, 1, 0, '{"key": "id", "type": "belongsToMany", "label": "name", "model": "App\\\\Models\\\\Lecturer", "pivot": null, "table": "lecturers", "column": "id", "taggable": null, "pivot_table": "awwards_lecturers"}', 18, 0);
