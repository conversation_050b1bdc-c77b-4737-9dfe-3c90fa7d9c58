$primary: #5c6bc0;
$error: #e54d42;
$success: #68cd86;
$warn: #ffb648;

// @font-face {
//   font-family: "icomoon";
//   src: url("./assets/fonts/icomoon.eot?7grlse");
//   src: url("./assets/fonts/icomoon.eot?7grlse#iefix")
//       format("embedded-opentype"),
//     url("./assets/fonts/icomoon.ttf?7grlse") format("truetype"),
//     url("./assets/fonts/icomoon.woff?7grlse") format("woff");
//   font-weight: normal;
//   font-style: normal;
// }

ul {
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

input:focus {
  outline: none;
}

input[disabled] {
  background-color: transparent;
}

// .vue-tags-input {
//   max-width: 450px;
//   position: relative;
//   background-color: #fff;
// }

div.vue-tags-input.disabled {
  opacity: 0.5;

  * {
    cursor: default;
  }
}

.ti-input {
  //   border: 1px solid #ccc;
  display: flex;
  //   padding: 4px;
  flex-wrap: wrap;
  border-radius: 0.1rem;
  outline: initial !important;
  box-shadow: initial !important;
  font-size: 0.8rem;
  padding: 0.35rem 0.75rem;
  line-height: 1;
  border: 1px solid #d7d7d7;
  background: white;
  color: #3a3a3a;
  border-color: #d7d7d7;
  min-height: calc(2em + 0.8rem);
}

.ti-tags {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  line-height: 1em;
  height: 100%;
}

.ti-tag {
  background-color: $primary;
  color: #fff;
  border-radius: 2px;
  display: flex;
  padding: 3px 5px;
  margin-right: 2px;
  margin-top: 1px;
  margin-bottom: 1px;
  font-size: 0.8rem;
  //   margin-bottom: 0;

  &:focus {
    outline: none;
  }

  .ti-content {
    display: flex;
    align-items: center;
  }

  .ti-tag-center {
    position: relative;
  }

  span {
    line-height: 0.85em;
  }

  span.ti-hidden {
    padding-left: 14px;
    visibility: hidden;
    height: 0px;
    white-space: pre;
  }

  .ti-actions {
    margin-left: 2px;
    display: flex;
    align-items: center;
    font-size: 1.15em;

    i {
      cursor: pointer;
    }
  }

  &:last-child {
    margin-right: 4px;
  }

  &.ti-invalid,
  &.ti-tag.ti-deletion-mark {
    background-color: $error;
  }
}

.ti-new-tag-input-wrapper {
  display: flex;
  flex: 1 0 auto;
  padding: 3px 5px;
  margin: 2px;
  font-size: 0.8rem;

  input {
    flex: 1 0 auto;
    min-width: 100px;
    border: none;
    padding: 0px;
    margin: 0px;
  }
}

.ti-new-tag-input {
  line-height: initial;
}

.ti-autocomplete {
  border: 1px solid #ccc;
  border-top: none;
  position: absolute;
  width: 100%;
  background-color: #fff;
  z-index: 20;
}

.ti-item > div {
  cursor: pointer;
  padding: 3px 6px;
  width: 100%;
}

.ti-selected-item {
  background-color: $primary;
  color: #fff;
}
