"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[776],{4776:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const l={setup:function(){}};const i=(0,s(1900).Z)(l,(function(){this._self._c;return this._m(0)}),[function(){var e=this,t=e._self._c;return t("div",{staticClass:"selected-library-item sfl-selected-item mb-5"},[t("div",{staticClass:"card d-flex flex-row media-thumb-container"},[t("a",{staticClass:"d-flex align-self-center"},[t("img",{staticClass:"list-media-thumbnail responsive border-0 sfl-selected-item-image",attrs:{src:"data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=",alt:"uploaded image"}})]),e._v(" "),t("div",{staticClass:"d-flex flex-grow-1 min-width-zero"},[t("div",{staticClass:"card-body align-self-center d-flex flex-column justify-content-between min-width-zero align-items-lg-center"},[t("a",{staticClass:"w-100"},[t("p",{staticClass:"list-item-heading mb-1 truncate sfl-selected-item-label"})])]),e._v(" "),t("div",{staticClass:"pl-1 align-self-center"},[t("a",{staticClass:"btn-link delete-library-item sfl-delete-item",attrs:{href:"#"}},[t("i",{staticClass:"simple-icon-trash"})])])])])])}],!1,null,null,null).exports}}]);