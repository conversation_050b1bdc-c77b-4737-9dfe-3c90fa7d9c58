{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"axios": "^0.21", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "postcss": "^8.1.14", "resolve-url-loader": "^5.0.0", "sass": "1.32.13", "sass-loader": "8.*", "vue": "^2.7.10", "vue-loader": "^15.10.0", "vue-template-compiler": "^2.7.10", "vue2-ace-editor": "^0.0.15"}, "dependencies": {"@popperjs/core": "^2.11.7", "bootstrap": "^4.6.2", "ckeditor4-vue": "^2.1.1", "datatables.net": "^1.12.1", "datatables.net-bs4": "^1.12.1", "datatables.net-dt": "^1.12.1", "datatables.net-responsive-bs4": "^2.3.0", "glightbox": "^3.2.0", "jquery": "^3.6.1", "path": "^0.12.7", "popper.js": "^1.16.1", "uuid": "^9.0.0", "vue-switches": "^2.0.1", "vue2-dropzone": "^3.6.0", "vuedraggable": "^2.24.3"}}