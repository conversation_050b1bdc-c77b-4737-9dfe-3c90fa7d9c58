<?php

namespace App\Http\Controllers;

use DB;
use App\Models\Form;
use App\Models\Course;
use App\Models\Enquiry;
use App\Models\Category;
use App\Models\FormData;
use App\Models\Location;
use App\Models\Conference;
use App\Models\Awward;
use Illuminate\Http\Request;
use App\Models\ConferencePackage;
use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\Mail;
use App\Http\Requests\SendEmailRequest;
use Illuminate\Support\Facades\Storage;

class ContactController extends Controller
{
    public function enquiry(Request $request)
    {
        // Create enquiry record
        $enquiryData = $request->all();

        // Map form fields to database fields for enquiry
        if ($request->has('name')) {
            $enquiryData['first_name'] = $request->name;
        }
        if ($request->has('surname')) {
            $enquiryData['last_name'] = $request->surname;
        }
        if ($request->has('phone')) {
            $enquiryData['mobile'] = $request->phone;
        }
        if ($request->has('job_title')) {
            $enquiryData['job'] = $request->job_title;
        }
        if ($request->has('message')) {
            $enquiryData['review'] = $request->message;
        }

        Enquiry::create($enquiryData);

        // If review data is provided, also create a review record
        if ($request->has('review_text') && !empty($request->review_text)) {
            $reviewData = [
                'name' => $request->name,
                'surname' => $request->surname,
                'email' => $request->email,
                'phone' => $request->phone,
                'company' => $request->company,
                'job_title' => $request->job_title,
                'review_text' => $request->review_text,
                'rating' => $request->rating,
                'type' => $request->type ?? 'award',
                'status' => 'pending'
            ];

            // Add related IDs based on type
            if ($request->has('award_id')) {
                $reviewData['award_id'] = $request->award_id;
            }

            Review::create($reviewData);
        }

        return back()->with('success', 'Thank you for your enquiry and review!');
    }

    public function form(Request $request, Form $form)
    {
        // Save to FormData table (existing functionality)
        FormData::create([
            'form_id' => $form->id,
            'data' => $request->all(),
            'model_type' => match($request->model_type){
                'conference' => Conference::class,
                'awward' => Awward::class,
                default => null
            },
            'model_id' => $request->model_id
        ]);

        // Also save to Enquiries table for admin panel visibility
        Enquiry::create([
            'first_name' => $request->input('First_Name') ?? $request->input('first_name'),
            'last_name' => $request->input('Last_Name') ?? $request->input('last_name'),
            'company' => $request->input('Company') ?? $request->input('company'),
            'job' => $request->input('Job') ?? $request->input('job'),
            'email' => $request->input('Email') ?? $request->input('email'),
            'mobile' => $request->input('Mobile') ?? $request->input('mobile'),
            'model_type' => match($request->model_type){
                'conference' => Conference::class,
                'awward' => Awward::class,
                default => null
            },
            'model_id' => $request->model_id,
            'type' => 'form_submission',
            'know_by' => $request->input('know_by') ?? $request->input('Know_by')
        ]);

        if($form->pdf){
            return Storage::download($form->pdf);
            return response()->download(Storage::url('app/'.$form->pdf), 'pdf', ['location' => '/home']);
        }
        return back();
    }

    public function send(SendEmailRequest  $request)
    {
        $validated = $request->validated();

        // Send email
        Mail::send([], [], function ($message) use ($validated) {
            $message->to('<EMAIL>')
                ->subject($validated['subject'])
                ->setBody(
                    "Name: {$validated['name']}\n" .
                    "Email: {$validated['email']}\n" .
                    "Phone: {$validated['number']}\n\n" .
                    "Message:\n{$validated['message']}",
                    'text/plain'
                );
        });

        return back()->with('success', 'Message sent successfully!');
    }

}
