<form class="cart">
    <div class="cart-title">
        <h2>@lang("common.cart_title") <a href="#" class="close-it show-cart"><i class="icofont-close"></i></a></h2>
    </div>
    @if($carts->count())
    @php $totalPrice = 0 @endphp
    <div class="cart-content" id="cart-body">
        @foreach($carts as $c)
        @php $totalPrice += $c->courseLocation->price * $c->qty @endphp
        <div class="c">
            <div class="c-t">
                <div class="title-init">
                    <a href="{{route('cart.destroy', $c)}}" class="cart-destroy delete-it">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM15.76 15.76C15.7 16.61 15.63 17.67 13.71 17.67H10.29C8.38 17.67 8.3 16.61 8.24 15.76L7.93 11.8C7.91 11.54 8 11.29 8.17 11.1C8.34 10.91 8.59 10.81 8.84 10.81H15.16C15.41 10.81 15.66 10.92 15.83 11.1C16 11.29 16.09 11.54 16.07 11.79L15.76 15.76ZM16.8 9.82C16.78 9.82 16.76 9.82 16.74 9.82C15.7 9.72 14.75 9.65 13.84 9.61C12.62 9.55 11.4 9.53 10.17 9.58C9.57 9.61 8.96 9.65 8.36 9.71L7.27 9.82C7.25 9.82 7.22 9.82 7.2 9.82C6.85 9.82 6.55 9.56 6.52 9.2C6.48 8.83 6.76 8.49 7.13 8.46L8.22 8.35C8.65 8.31 9.07 8.28 9.5 8.26L9.58 7.79C9.66 7.29 9.81 6.33 11.31 6.33H12.7C14.21 6.33 14.36 7.32 14.43 7.8L14.51 8.28C15.26 8.32 16.03 8.38 16.87 8.46C17.25 8.5 17.52 8.83 17.49 9.21C17.45 9.56 17.15 9.82 16.8 9.82Z" fill="#292D32"></path>
                        </svg>
                    </a>
                    <figure>
                        <img src="{{ Storage::url($c->courseLocation->course->category->image) }}" alt="">
                    </figure>
                    <div class="c-title__tile">
                        <h3>{{$c->courseLocation->course->title}}</h3>
                    </div>
                </div>
                <div class="c-t__infos">
                    <div class="country">
                        <img src="{{Storage::url($c->courseLocation->location->image)}}" alt="">
                        <h2>{{$c->courseLocation->location->name}}</h2>
                    </div>
                    <div class="date">
                        <span>
                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 10V7C20 5.89543 19.1046 5 18 5H6C4.89543 5 4 5.89543 4 7V10M20 10V19C20 20.1046 19.1046 21 18 21H6C4.89543 21 4 20.1046 4 19V10M20 10H4M8 3V7M16 3V7" stroke="#000000" stroke-width="2" stroke-linecap="round"></path>
                                <rect x="6" y="12" width="3" height="3" rx="0.5" fill="#000000"></rect>
                                <rect x="10.5" y="12" width="3" height="3" rx="0.5" fill="#000000"></rect>
                                <rect x="15" y="12" width="3" height="3" rx="0.5" fill="#000000"></rect>
                            </svg>
                            <div class="title">
                             {{ \Carbon\Carbon::parse($c->courseLocation->start_at)->format('Y-m-d') }} -  {{ \Carbon\Carbon::parse($c->courseLocation->end_at)->format('Y-m-d') }}
                            </div>
                        </span>
                    </div>
                </div>
            </div>
            <div class="c-info">
                <p data-price="4900">£{{$c->courseLocation->price}}</p>
                <div class="quantify number">
                    <span class="qt-minus minus"><i class="icofont-minus"></i></span>
                    <input data-id="{{$c->id}}" data-route="{{route('cart.update', $c)}}" class="qt qt-input" value="{{$c->qty}}" readonly>
                    <span class="qt-plus plus"><i class="icofont-plus"></i></span>
                </div>
            </div>
        </div>
        @endforeach
    </div>
    <div class="all-sum">
        <span>@lang("common.total_sum")</span>
        <p id="sum" class="cart-total-price">£{{$totalPrice}}</p>
    </div>
    <div class="d-flex justify-content-center float-left w-100">
        <a href="{{route('cart.index')}}" class="register-now">Checkout</a>
    </div>
    @else
        <div class="nocart">
            <svg xmlns="http://www.w3.org/2000/svg" width="356.000000pt" height="246.000000pt" viewBox="0 0 356.000000 246.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,246.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M1770 1989 c-178 -31 -333 -182 -370 -361 l-11 -58 -110 0 c-81 0 -109 -3 -109 -12 0 -38 -51 -975 -54 -983 -2 -7 -95 -18 -222 -28 -262 -21-501 -47 -531 -58 -23 -9 -31 -33 -13 -44 5 -3 89 3 187 14 98 11 241 25 318 31 77 6 167 13 199 16 l59 5 -6 -120 -5 -121 748 0 747 0 -4 73 c-2 39 -5 93 -7 120 -2 38 0 47 13 47 47 0 436 -34 575 -50 89 -10 165 -14 169 -10 5 4 7 16 5 26 -3 16 -19 21 -98 31 -142 17 -410 42 -543 49 -71 4 -120 12 -123 18 -4 11 -54 902 -54 964 l0 32 -110 0 -110 0 -6 28 c-17 78 -28 111 -49 152 -88 172 -292 272 -485 239z m233 -102 c111 -51 206 -170 224 -282 l6 -35 -383 0 -383 0 7 43 c20 129 128 246 271 294 65 21 189 12 258 -20z m-613 -447 l0 -60 35 0 34 0 3 58 3 57 383 3 382 2 0 -60 0 -60 40 0 40 0 0 60 0 60 70 0 c48 0 72 -4 75 -12 2 -7 15 -236 29 -508 14 -272 28 -525 31 -562 l6 -68 -670 0 c-369 0 -671 1 -671 3 0 1 13 250 30 552 16 303 30 560 30 573 0 21 4 22 75 22 l75 0 0 -60z"></path><path d="M1550 875 c-28 -34 12 -85 54 -69 22 8 31 50 16 69 -16 19 -54 19 -70 0z"></path><path d="M2082 878 c-27 -27 -7 -78 29 -78 21 0 49 26 49 45 0 35 -53 58 -78 33z"></path><path d="M1853 690 c-35 -21 -59 -58 -46 -71 11 -11 225 20 241 36 11 10 9 16 -14 34 -36 29 -135 30 -181 1z"></path></g></svg>
            <p>@lang("common.cart_empty")</p>
        </div>
    @endif

</form>