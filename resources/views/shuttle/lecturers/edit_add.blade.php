@extends('shuttle::admin')

@section('main')
<div class="row">
    <div class="col-md-12">
        <x-shuttle-form
                :action="$dataTypeContent->id ? route('shuttle.scaffold_interface.update', ['scaffold_interface' => $scaffold_interface, 'id' => $dataTypeContent->id, 'lang' => $lang]) : route('shuttle.scaffold_interface.store',$scaffold_interface)"
                :scaffold-interface-rows="$scaffold_interface->rows"
                :data-type-content="$dataTypeContent"
                :edit="$dataTypeContent->id ? true : false"
        >
            <x-slot name="contacts">
                <div class="form-group col-md-6">
                    <label for="name" class="control-label">Mobile</label>
                    <input type="text" name="contacts[mobile]" value="{{data_get($dataTypeContent->contacts, 'mobile')}}" class="form-control" />
                </div>
                <div class="form-group col-md-6">
                    <label for="name" class="control-label">Whatsapp</label>
                    <input type="text" name="contacts[whatsapp]" value="{{data_get($dataTypeContent->contacts, 'whatsapp')}}" class="form-control" />
                </div>
                <div class="form-group col-md-4">
                    <label for="name" class="control-label">Facebook</label>
                    <input type="text" name="contacts[fb]" value="{{data_get($dataTypeContent->contacts, 'fb')}}" class="form-control" />
                </div>
                <div class="form-group col-md-4">
                    <label for="name" class="control-label">Twitter</label>
                    <input type="text" name="contacts[twitter]" value="{{data_get($dataTypeContent->contacts, 'twitter')}}" class="form-control" />
                </div>
                <div class="form-group col-md-4">
                    <label for="name" class="control-label">Linkedin</label>
                    <input type="text" name="contacts[linkedin]" value="{{data_get($dataTypeContent->contacts, 'linkedin')}}" class="form-control" />
                </div>
            </x-slot>
        </x-shuttle-form>
    </div>
</div>
@stop