<?php

namespace App\Http\Controllers;

use App\Domains\PasswordReset\Models\PasswordReset;
use App\Domains\SmsSender\Services\SmsSenderService;
use App\Domains\User\Helpers\OAuthValidator;
use App\Domains\User\Services\UserService;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RegisterController extends Controller
{

    public function __construct()
    {
        $this->middleware('guest');
    }

    public function store(Request $request)
    {
        
        $request->validate([
            'first_name'   => 'required|string',
            'last_name'    => 'sometimes|nullable|string',
            'password'     => "required|confirmed",
            'email'        => 'required|email|unique:users',
            'mobile'       => 'required|unique:users',
            //'id_number'    => 'required|min:11|max:11|unique:users',
        ]);

        $user = User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            //'id_number' => $request->id_number,
            'mobile' => $request->mobile,
            'email' => $request->email,
            'password' => bcrypt($request->password),
        ]);

        Auth::login($user);
        
        return redirect()->route('profile');
    }


}
