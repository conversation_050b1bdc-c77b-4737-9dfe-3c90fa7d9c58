<?php

namespace Sina\Shuttle\FormFields;

class NumberHandler extends <PERSON>bstract<PERSON><PERSON><PERSON>
{
    protected $codename = 'number';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.number', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
