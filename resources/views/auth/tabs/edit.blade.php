<section class="dashboard">
    <div class="container">
        <div class="row">
            <div class="col-md-12 col-xl-12">
                <div class="dashboard-content">
                    <div class="dashboard-content__title">
                        <h3>@lang('common.profile_edit')</h3>
                    </div>
                    <div class="dashboard-content__content">
                        @error('success')
                            <div class="alert alert-success">
                                @lang('common.succes_edit')
                            </div>
                        @enderror

                        <div class="dashboard-content__edit">
                            <form action="{{ route('profile.update') }}" method="post" enctype="multipart/form-data">
                                @csrf
                                @method('put')
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="edit-profile__avatar mb-4">
                                            <div class="edit-profile__avatar-img">
                                            <img src="{{ asset('storage/' . str_replace('public/', '', $user->image)) }}" alt="Avatar" id="avatar">                                            </div>
                                            <div class="edit-profile__avatar-info">
                                                <a href="#" id="upload_link">@lang('common.upload_photo')</a>
                                                <input name="image" id="upload" type="file">
                                                <figcaption>@lang('common.choose_from_ur_images')</figcaption>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="loginform-form">
                                            <label for="first_name">Your Name</label>
                                            <input name="first_name" id="first_name" class="f-input" type="text"
                                                value="{{ old('first_name', $user->first_name) }}">
                                            @error('first_name')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="loginform-form">
                                            <label for="last_name">Your Surname</label>
                                            <input name="last_name" id="last_name" class="f-input"
                                                value="{{ old('last_name', $user->last_name) }}">
                                            @error('last_name')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="loginform-form">
                                            <label for="email">Your Email</label>
                                            <input name="email" id="email" class="f-input" type="email"
                                                value="{{ old('email', $user->email) }}">
                                            @error('email')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="loginform-form">
                                            <label for="mobile">Your Mobile</label>
                                            <input name="mobile" id="mobile" class="f-input" type="number"
                                                value="{{ old('mobile', $user->mobile) }}">
                                            @error('mobile')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="loginform-form">
                                            <label for="address">Your Address</label>
                                            <input name="address" id="address" class="f-input"
                                                value="{{ old('address', $user->address) }}">
                                            @error('address')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="loginform-form">
                                            <label for="password">New Password</label>
                                            <input name="password" id="password" class="f-input" type="password">
                                            @error('password')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="loginform-form">
                                            <label for="password_confirm">Repeat New Password</label>
                                            <input name="password_confirmation" id="password_confirm" class="f-input"
                                                type="password">
                                            @error('password_confirmation')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12 d-flex justify-content-end mt-3">
                                        <button type="submit" class="btn-submit edit-btn">Edit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
