@extends('shuttle::admin')
@section('breadcrumbs')
    @include('shuttle::includes.breadcrumbs', ['breadCrumbs' => ['Home' => route('shuttle.index'),
    $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)], 'btn' =>
    ($add) ? route('shuttle.scaffold_interface.create',$scaffoldInterface) : null])
@stop

@section('main')

    <div class="row mb-5">
        <div class="col-md-12">
            <div class="card">
                <form class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <input name="q" type="text" class="form-control" placeholder="Search Word">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <select name="category_id" id="" class="form-control">
                                <option value="">Choose Category</option>
                                @foreach(\App\Models\Category::all() as $cat)
                                <option value="{{$cat->id}}">{{$cat->name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <input name="start_at" type="date" class="form-control" placeholder="Start">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <input name="end_at" type="date" class="form-control" placeholder="End">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <button class="btn btn-primary" type="submit">Filter</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @include('shuttle::scaffold.index_default')
@stop
