<?php
// Check if the awward_hasmany_awward_package_relationship field exists
// Upload this file to your server root and visit it in browser

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Check if the relationship field exists
    $result = DB::table('shuttle_scaffold_interface_rows')
        ->where('rowable_id', 16)
        ->where('field', 'awward_hasmany_awward_package_relationship')
        ->first();
    
    if ($result) {
        echo "✅ SUCCESS: awward_hasmany_awward_package_relationship field found!<br>";
        echo "Field details:<br>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    } else {
        echo "❌ ERROR: awward_hasmany_awward_package_relationship field NOT found!<br>";
        echo "Please run the SQL command again.<br>";
    }
    
    // Show all fields for awwards (rowable_id = 16)
    echo "<hr>";
    echo "<h3>All fields for Awards (rowable_id = 16):</h3>";
    $allFields = DB::table('shuttle_scaffold_interface_rows')
        ->where('rowable_id', 16)
        ->orderBy('ord')
        ->get();
    
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Field</th><th>Type</th><th>Display Name</th><th>Edit</th><th>Add</th></tr>";
    foreach ($allFields as $field) {
        echo "<tr>";
        echo "<td>{$field->id}</td>";
        echo "<td>{$field->field}</td>";
        echo "<td>{$field->type}</td>";
        echo "<td>{$field->display_name}</td>";
        echo "<td>" . ($field->edit ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($field->add ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage();
}
?>
