<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Models\AwwardPackage;
use App\Models\FestivalPackage;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\ConferencePackage;
use App\Service\TransactionService;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Http\Requests\TransactionRequest;
use Illuminate\Support\Facades\Mail;
use App\Mail\InvoiceEmail;
use App\Models\Cart;

require_once base_path('vendor/stripe/stripe-php/init.php');

class TransactionController extends Controller
{
    private $transactionService;
    
    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
        $this->middleware('auth');
    }

    public function store(TransactionRequest $request)
    {
        $user = auth()->user();
        $validated = $request->validated();
        $cart = $this->transactionService->getCart($user);
        
        Log::info('Cart items', [
            'user_id' => $user->id,
            'cart_items' => $cart->toArray(),
        ]);

        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Cart is empty or contains invalid items.',
            ], 400);
        }

        try {
            // $transaction = null;

            if ($request->payment_method === 'invoice') {
                Log::info('Attempting to generate invoice...');
                Log::info('Request data:', $request->all());
                Log::info('Cart data:', $cart->toArray());
                
                // Debug cart structure
                Log::info('Cart structure debug:', [
                    'cart_count' => $cart->count(),
                    'first_item_structure' => $cart->first() ? [
                        'id' => $cart->first()->id,
                        'qty' => $cart->first()->qty,
                        'has_courseLocation' => isset($cart->first()->courseLocation),
                        'courseLocation_id' => $cart->first()->courseLocation->id ?? 'N/A',
                        'has_course' => isset($cart->first()->courseLocation->course),
                        'course_title' => $cart->first()->courseLocation->course->title ?? 'N/A',
                        'has_location' => isset($cart->first()->courseLocation->location),
                        'location_name' => $cart->first()->courseLocation->location->name ?? 'N/A',
                    ] : 'No items in cart'
                ]);
                
                try {
                    $invoiceNumber = 'INV-' . time();
                    $paymentDeadline = now()->addDays(7)->format('Y-m-d');

                    $subtotalPrice = $cart->sum(function($item) {
                        return $item->qty * $item->courseLocation->price;
                    });
                    $vatRate = 0.20;
                    $vatPrice = $subtotalPrice * $vatRate;
                    $totalPrice = $subtotalPrice + $vatPrice; // Calculate total price correctly

                    // Define the VAT number here or retrieve it from configuration
                    $companyVatNumber = 'GBP 429 2217 04'; // Example VAT number

                    // Debug mapped cart items
                    $mappedItems = $cart->map(function ($item) {
                        return [
                            'title' => $item->courseLocation->course->title ?? 'No Title',
                            'location' => $item->courseLocation->location->name ?? 'No Location',    
                            'start_at' => $item->courseLocation->start_at ?? 'N/A',
                            'end_at' => $item->courseLocation->end_at ?? 'N/A',
                            'price' => ($item->courseLocation->price ?? 0) * $item->qty,
                        ];
                    })->toArray();
                    
                    Log::info('Mapped cart items for PDF:', $mappedItems);

                    // PDF generation - გააქტიურება
                    // Log::info('Activating PDF generation...');
                    // dd($cart->map(function ($item) {
                    //     return [
                    //         'title' => $item->courseLocation->course->title ?? 'No Title',
                    //         'location' => $item->courseLocation->location->name ?? 'No Location',
                    //         'start_at' => $item->courseLocation->start_at ?? 'N/A',
                    //         'end_at' => $item->courseLocation->end_at ?? 'N/A',
                    //         'price' => $item->courseLocation->price ?? 0,
                    //     ];
                    // })->toArray());
                    // !!! არ დაგავიწყდეთ ამ dd() ხაზის წაშლა, როგორც კი პრობლემა მოგვარდება !!!
                    
                    // Calculate final amount with promo code
                    $finalAmount = $request->final_amount ?? $request->subtotal_price;
                    $promoCode = $request->promo_code ?? null;
                    $promoDiscountPercentage = $request->promo_discount_percentage ?? 0;
                    $promoDiscountAmount = $request->promo_discount_amount ?? 0;

                    Log::info('Promo code data:', [
                        'promo_code' => $promoCode,
                        'discount_percentage' => $promoDiscountPercentage,
                        'discount_amount' => $promoDiscountAmount,
                        'final_amount' => $finalAmount
                    ]);

                    // Correctly load PDF view with necessary data, including promo code
                    $pdf = Pdf::loadView('invoices.pdf', [
                        'customerName' => $user->name,
                        'invoiceNumber' => $invoiceNumber,
                        'paymentDeadline' => $paymentDeadline,
                        'vat_number' => $companyVatNumber,
                        'name_surname' => $request->name,
                        'job_title' => $request->job,
                        'company' => $request->company,
                        'address' => $request->address1 . ($request->address2 ? ', ' . $request->address2 : '') . ', ' . $request->city . ', ' . $request->zip_code . ', ' . $request->country,
                        'email' => $request->email,
                        'phone' => $request->mobile,
                        'subtotal_price' => (float) $request->subtotal_price,
                        'vat_price' => (float) $request->vat_price,
                        'total_price' => (float) $request->total_price,
                        'final_amount' => (float) $finalAmount,
                        'promo_code' => $promoCode,
                        'promo_discount_percentage' => (float) $promoDiscountPercentage,
                        'promo_discount_amount' => (float) $promoDiscountAmount,
                        'cart_items' => $mappedItems,
                    ]);
                    
Log::info('PDF view loaded successfully');

                    // --- START: CORRECTED PATH HANDLING ---
                    $pdfFileName = 'invoice-' . $invoiceNumber . '.pdf';
                    // Define the directory for invoices
                    $invoiceDirectory = 'invoices'; 
                    $fullPdfDirectoryPath = storage_path('app/' . $invoiceDirectory);

                    // Ensure the invoice directory exists
                    if (!file_exists($fullPdfDirectoryPath)) {
                        mkdir($fullPdfDirectoryPath, 0755, true);
                        Log::info('Created invoice directory: ' . $fullPdfDirectoryPath);
                    }

                    // Construct the full path where the PDF will be saved
                    $fullPdfPath = $fullPdfDirectoryPath . '/' . $pdfFileName;
                    
                    // Save the PDF
                    $pdf->save($fullPdfPath);
                    
                    // Verify file existence after saving
                    if (file_exists($fullPdfPath)) {
                        Log::info('SUCCESS: PDF saved at: ' . $fullPdfPath);
                    } else {
                        Log::error('FAILED: PDF was not saved to ' . $fullPdfPath);
                        throw new \Exception('PDF file was not created successfully');
                    }
                    
                    // Define the relative path to be stored in the database
                    $pdfRelativePathForDb = $invoiceDirectory . '/' . $pdfFileName;
                    // --- END: CORRECTED PATH HANDLING ---


                    // Email გაგზავნა
                    try {
                        Log::info("Attempting to send invoice email to: {$request->email}");

                        Mail::to($request->email)->send(new InvoiceEmail(
                            $fullPdfPath,
                            $invoiceNumber,
                            $paymentDeadline,
                            $request->name
                        ));

                        Log::info('Invoice email sent successfully to: ' . $request->email);
                    } catch (\Exception $e) {
                        Log::error('Failed to send invoice email: ' . $e->getMessage());
                        Log::error('Email error trace: ' . $e->getTraceAsString());
                    }


                    // Transaction შექმნა
                    $transaction = $user->transactions()->create([
                        'name' => $request->name,
                        'email' => $request->email,
                        'mobile' => $request->mobile,
                        'company' => $request->company,
                        'job' => $request->job,
                        'address1' => $request->address1,
                        'address2' => $request->address2,
                        'city' => $request->city,
                        'zip_code' => $request->zip_code,
                        'country' => $request->country,
                        'payment_method' => 'invoice',
                        'price' => $finalAmount, // Use final amount with promo discount
                        'promo_code' => $promoCode,
                        'promo_discount_percentage' => $promoDiscountPercentage,
                        'promo_discount_amount' => $promoDiscountAmount,
                        'success' => true,
                        'file_path' => $pdfRelativePathForDb, // <--- Corrected variable here
                        'file_name' => $pdfFileName,
                    ]);

                    // Cart items დამატება
                    foreach ($cart as $item) {
                        // Ensure course and location exist before trying to access properties
                        if ($item->courseLocation && $item->courseLocation->course) {
                            $transaction->items()->create([
                                'model_type' => get_class($item->courseLocation->course),
                                'model_id' => $item->courseLocation->course->id,
                                'price' => $item->courseLocation->price * $item->qty,
                                'user_id' => $user->id
                            ]);
                        } else {
                            Log::warning('Cart item has null courseLocation or missing course property when creating transaction item', [
                                'cart_item_id' => $item->id ?? 'N/A',
                                'user_id' => $user->id,
                            ]);
                        }
                    }

                    // წაშალე ყველა cart item ამ მომხმარებლისთვის
                    Cart::where('user_id', $user->id)->delete();

                    Log::info('Transaction created successfully with ID: ' . $transaction->id);

                    // Redirect to success page
                    return response()->json([
                        'success' => true,
                        'message' => 'Invoice generated successfully and payment process completed.',
                        'redirect_url' => route('transaction.success', $transaction),
                    ]);

                } catch (\Exception $e) {
                    Log::error('PDF generation or invoice transaction creation failed: ' . $e->getMessage());
                    Log::error('Full error trace: ' . $e->getTraceAsString());
                    Log::error('Error at line: ' . $e->getLine() . ' in file: ' . $e->getFile());
                    return response()->json([
                        'success' => false,
                        'message' => 'Invoice generation failed: ' . $e->getMessage(),
                    ], 500);
                }
            } else {
                Log::info('Attempting to handle Stripe payment...');
                $transaction = $this->transactionService->handleStripePayment($user, $request, $cart);

                // წაშალე cart
                Cart::where('user_id', $user->id)->delete();

                return response()->json([
                    'success' => true,
                    'message' => 'Payment successful!',
                    'redirect_url' => route('transaction.success', $transaction),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Transaction failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'request_data' => $request->all(),
                'cart' => $cart->toArray(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Transaction failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function success(Request $request, Transaction $transaction){
        return view('payment.success', compact('transaction'));
    }

    public function invoice(Request $request, Transaction $transaction)
    {

        $path = storage_path('app/' . $transaction->file_path);

        if (!file_exists($path)) {
            abort(404, 'Invoice file not found.');
        }

        return response()->download($path, $transaction->file_name ?? basename($path));
    }

    public function conference(Request $request, ConferencePackage $package)
    {
        $user = auth()->user();

        // Calculate price with VAT if UK location
        $finalPrice = 0;
        if (optional($package->conference)->location_id == 1) {
            $finalPrice = $package->price * 1.2; // Add 20% VAT for UK
        } else {
            $finalPrice = $package->price;
        }

        // Check payment method
        if ($request->pm === 'invoice') {
            // Invoice payment
            $transaction = $user->transactions()->create([
                'name' => $request->fullName ?? $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'company' => $request->company,
                'job' => $request->job,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'payment_method' => 'invoice',
                'price' => $finalPrice,
                'success' => false, // Will be true when invoice is paid
            ]);

            $transaction->items()->create([
                'model_type' => ConferencePackage::class,
                'model_id' => $package->id,
                'price' => $finalPrice,
                'user_id' => $user->id
            ]);

            // Generate and send invoice
            $this->generateAndSendInvoice($transaction, $package->title);

            return redirect()->route('transaction.success', $transaction);
        } else {
            // Stripe payment
            try {
                \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

                $priceInPence = $finalPrice * 100;

                $charge = \Stripe\Charge::create([
                    "amount" => $priceInPence,
                    "currency" => "gbp",
                    "source" => $request->stripeToken,
                    "description" => "Conference Package: " . $package->title,
                    "receipt_email" => $request->email,
                ]);

                $transaction = $user->transactions()->create([
                    'name' => $request->fullName ?? $request->name,
                    'email' => $request->email,
                    'mobile' => $request->mobile,
                    'company' => $request->company,
                    'job' => $request->job,
                    'address1' => $request->address1,
                    'address2' => $request->address2,
                    'city' => $request->city,
                    'zip_code' => $request->zip_code,
                    'country' => $request->country,
                    'payment_method' => 'card',
                    'price' => $finalPrice,
                    'success' => true,
                    'stripe_id' => $charge->id,
                ]);

                $transaction->items()->create([
                    'model_type' => ConferencePackage::class,
                    'model_id' => $package->id,
                    'price' => $finalPrice,
                    'user_id' => $user->id
                ]);

                return redirect()->route('transaction.success', $transaction);

            } catch (\Exception $e) {
                \Log::error('Stripe Payment failed for conference', ['error' => $e->getMessage()]);
                return back()->with('error', 'Payment failed: ' . $e->getMessage());
            }
        }
    }

    public function festival(Request $request, FestivalPackage $package)
    {
        $user = auth()->user();

        if ($request->pm === 'invoice') {
            // Placeholder for invoice logic if 'pm' is used.
        }

        try {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));


            $priceInPence = 0;
            if(optional($package->festival)->location_id == 1){
                $priceInPence = ($package->price * 1.2) * 100;
            } else {
                $priceInPence = $package->price * 100;
            }

            $charge = \Stripe\Charge::create([
                "amount" => $priceInPence,
                "currency" => "gbp",
                "source" => $request->stripeToken,
                "description" => "Festival Package: " . $package->title,
                "receipt_email" => $request->email,
            ]);

            $transaction = $user->transactions()->create([
                'name' => $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'company' => $request->company,
                'job' => $request->job,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'payment_method' => 'card',
                'price' => $package->price,
                'success' => true,
                'stripe_id' => $charge->id,
            ]);

            $transaction->items()->create([
                'model_type' => FestivalPackage::class,
                'model_id' => $package->id,
                'price' => $package->price,
                'user_id' => $user->id
            ]);

            return redirect()->route('transaction.success', $transaction);

        } catch (\Exception $e) {
            Log::error('Stripe Payment failed for festival', ['error' => $e->getMessage()]);
            return back()->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }

    public function awward(Request $request, AwwardPackage $package)
    {
        $user = auth()->user();

        // Calculate total price with VAT (always add 20% VAT for awards)
        $basePrice = $package->price; // Base price without VAT
        $vatAmount = $basePrice * 0.20; // 20% VAT
        $totalPrice = $basePrice + $vatAmount; // Total price including VAT

        // Handle promo code if applied
        $finalAmount = $totalPrice;
        $promoCode = $request->promo_code_applied ?? null;
        $promoDiscountPercentage = $request->promo_discount_percentage ?? 0;
        $promoDiscountAmount = $request->promo_discount_amount ?? 0;

        if ($promoCode && $request->final_amount) {
            $finalAmount = (float) $request->final_amount;
        }

        Log::info('Awards payment with promo code:', [
            'package_id' => $package->id,
            'base_price' => $basePrice,
            'total_price' => $totalPrice,
            'promo_code' => $promoCode,
            'discount_percentage' => $promoDiscountPercentage,
            'discount_amount' => $promoDiscountAmount,
            'final_amount' => $finalAmount
        ]);

        // Check payment method
        if ($request->pm === 'invoice') {
            // Invoice payment
            $transaction = $user->transactions()->create([
                'name' => $request->fullName ?? $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'company' => $request->company,
                'job' => $request->job,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'payment_method' => 'invoice',
                'price' => $finalAmount, // Use final amount with promo discount
                'promo_code' => $promoCode,
                'promo_discount_percentage' => $promoDiscountPercentage,
                'promo_discount_amount' => $promoDiscountAmount,
                'success' => false, // Will be true when invoice is paid
            ]);

            $transaction->items()->create([
                'model_type' => AwwardPackage::class,
                'model_id' => $package->id,
                'price' => $finalAmount, // Use final amount with promo discount
                'user_id' => $user->id
            ]);

            // Generate and send invoice
            $this->generateAndSendInvoice($transaction, $package->title);

            return redirect()->route('transaction.success', $transaction);
        } else {
            // Stripe payment
            try {
                \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

                $priceInPence = $finalAmount * 100; // Use final amount with promo discount

                $charge = \Stripe\Charge::create([
                    "amount" => $priceInPence,
                    "currency" => "gbp",
                    "source" => $request->stripeToken,
                    "description" => "Awards Package: " . $package->title . ($promoCode ? " (Promo: {$promoCode})" : ''),
                    "receipt_email" => $request->email,
                ]);

                $transaction = $user->transactions()->create([
                    'name' => $request->fullName ?? $request->name,
                    'email' => $request->email,
                    'mobile' => $request->mobile,
                    'company' => $request->company,
                    'job' => $request->job,
                    'address1' => $request->address1,
                    'address2' => $request->address2,
                    'city' => $request->city,
                    'zip_code' => $request->zip_code,
                    'country' => $request->country,
                    'payment_method' => 'card',
                    'price' => $finalAmount, // Use final amount with promo discount
                    'promo_code' => $promoCode,
                    'promo_discount_percentage' => $promoDiscountPercentage,
                    'promo_discount_amount' => $promoDiscountAmount,
                    'success' => true,
                    'stripe_id' => $charge->id,
                ]);

                $transaction->items()->create([
                    'model_type' => AwwardPackage::class,
                    'model_id' => $package->id,
                    'price' => $finalAmount, // Use final amount with promo discount
                    'user_id' => $user->id
                ]);

                return redirect()->route('transaction.success', $transaction);

            } catch (\Exception $e) {
                \Log::error('Stripe Payment failed for awards', ['error' => $e->getMessage()]);
                return back()->with('error', 'Payment failed: ' . $e->getMessage());
            }
        }
    }

    // Helper method to generate and send invoice
    private function generateAndSendInvoice($transaction, $packageTitle)
    {
        try {
            // Generate invoice number
            $invoiceNumber = 'INV-' . date('Y') . '-' . str_pad($transaction->id, 6, '0', STR_PAD_LEFT);
            $paymentDeadline = now()->addDays(7)->format('Y-m-d');

            // Create invoice directory if it doesn't exist
            $invoiceDirectory = storage_path('app/invoices');
            if (!file_exists($invoiceDirectory)) {
                mkdir($invoiceDirectory, 0755, true);
            }

            // Prepare data for existing invoice template
            // The transaction price includes VAT (20%)
            $totalPriceWithVat = $transaction->price;
            $vatRate = 0.20;
            $basePriceWithoutVat = $totalPriceWithVat / (1 + $vatRate); // Calculate base price without VAT
            $vatAmount = $totalPriceWithVat - $basePriceWithoutVat; // Calculate VAT amount

            // Generate PDF using existing template
            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('invoices.pdf', [
                'invoiceNumber' => $invoiceNumber,
                'vat_number' => 'GBP 429 2217 04',
                'name_surname' => $transaction->name,
                'job_title' => $transaction->job ?? 'Professional',
                'company' => $transaction->company,
                'address' => $transaction->address1 . ($transaction->address2 ? ', ' . $transaction->address2 : '') . ', ' . $transaction->city . ', ' . $transaction->zip_code,
                'email' => $transaction->email,
                'phone' => $transaction->mobile,
                'cart_items' => [[
                    'title' => $packageTitle,
                    'location' => 'London, UK',
                    'start_at' => now()->format('d/m/Y'),
                    'end_at' => now()->addDays(1)->format('d/m/Y'),
                ]],
                'subtotal_price' => $totalPriceWithVat, // Total price including VAT (this is what template expects)
                'vat_price' => $vatAmount, // VAT amount
            ]);

            $fileName = $invoiceNumber . '.pdf';
            $fullPdfPath = $invoiceDirectory . '/' . $fileName;
            $pdf->save($fullPdfPath);

            // Update transaction with invoice info
            $transaction->update([
                'file_path' => 'invoices/' . $fileName,
                'file_name' => $fileName,
            ]);

            // Send email with invoice
            \Illuminate\Support\Facades\Mail::to($transaction->email)->send(new \App\Mail\InvoiceEmail(
                $fullPdfPath,
                $invoiceNumber,
                $paymentDeadline,
                $transaction->name
            ));

            \Log::info('Invoice email sent successfully for transaction: ' . $transaction->id);

        } catch (\Exception $e) {
            \Log::error('Failed to generate or send invoice: ' . $e->getMessage());
            \Log::error('Invoice error trace: ' . $e->getTraceAsString());
        }
    }
}