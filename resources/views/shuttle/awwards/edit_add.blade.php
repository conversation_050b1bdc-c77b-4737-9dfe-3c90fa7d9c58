@extends('shuttle::admin')

@section('main')
<div class="row">
    <div class="col-md-12">
        <x-shuttle-form
                :action="$dataTypeContent->id ? route('shuttle.scaffold_interface.update', ['scaffold_interface' => $scaffold_interface, 'id' => $dataTypeContent->id, 'lang' => $lang]) : route('shuttle.scaffold_interface.store',$scaffold_interface)"
                :scaffold-interface-rows="$scaffold_interface->rows"
                :data-type-content="$dataTypeContent"
                :edit="$dataTypeContent->id ? true : false"
        >

            <x-slot name="awward_hasmany_awward_package_relationship">
                <div class="form-group col-md-12 " id="media_lib_app">
                    <table class="table table-striped table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th scope="col">Title</th>
                                <th scope="col">Start Date</th>
                                <th scope="col">End Date</th>
                                <th scope="col">Price</th>
                                <th class="text-right">
                                    <button class="btn btn-primary icon-button addbtn" type="button">
                                        <i class="simple-icon-plus"></i>
                                    </button>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="other-chance-ticket-table">
                            @if(isset($dataTypeContent->packages) && $dataTypeContent->packages->count() > 0)
                                @foreach ($dataTypeContent->packages as $item)
                                    <tr>
                                        <td>
                                            <input name="locations[{{$loop->index}}][id]" value="{{$item->id}}" hidden="">
                                            <input class="form-control" name="locations[{{$loop->index}}][title]" value="{{$item->title}}" placeholder="Package Title" />
                                        </td>
                                        <td>
                                            <input class="form-control" name="locations[{{$loop->index}}][start_at]" type="date" value="{{$item->start_at?->format('Y-m-d')}}" />
                                        </td>
                                        <td>
                                            <input class="form-control" name="locations[{{$loop->index}}][end_at]" type="date" value="{{$item->end_at?->format('Y-m-d')}}" />
                                        </td>
                                        <td>
                                            <input class="form-control" name="locations[{{$loop->index}}][price]" value="{{$item->price}}" placeholder="0.00" />
                                        </td>
                                        <td class="text-right">
                                            <button class="btn btn-danger icon-button trashBtn" type="button">
                                                <i class="simple-icon-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <em>No packages yet. Click the + button to add packages.</em>
                                    </td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </x-slot>
        </x-shuttle-form>
    </div>

    @if($dataTypeContent->id)
    <div class="col-md-12">
        <div class="cm">
            <div class="cm-title">
                <h1>Components</h1>
                <form class="cm-content" method="post" action="{{route('shuttle.awwards.component', $dataTypeContent->id)}}">
                    @csrf
                    <select class="form-control" name="component_id" id="">
                        <option value="">Choose Component</option>
                        @foreach(\Sina\Shuttle\Models\Component::all() as $c)
                        <option value="{{$c->id}}">{{$c->name}}</option>
                        @endforeach
                    </select>
                    <button class="btn btn-primary" type="submit">Add New</button>
                </form>
            </div>
        </div>

        <div class="cm-area">
            <div class="row sortable">
                @if($dataTypeContent->components->count())
                    @foreach($dataTypeContent->components as $c)
                <div class="col-md-4" id="{{$c->pivot->id}}">
                    <div id="1" class="mt-2 ui-sortable-handle">
                        <div class="compo-item">
                            <figure>
                                <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_429_11113)">
                                        <path d="M12 2.99994L15 5.99994L12 8.99994L9 5.99994L12 2.99994Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"></path>
                                        <path d="M12 14.9999L15 17.9999L12 20.9999L9 17.9999L12 14.9999Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"></path>
                                        <path d="M21 8.99994L18 5.99994L21 2.99994L24 5.99994L21 8.99994Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"></path>
                                        <path d="M21 20.9999L18 17.9999L21 14.9999L24 17.9999L21 20.9999Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"></path>
                                        <path d="M3 8.99994L0 5.99994L3 2.99994L6 5.99994L3 8.99994Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"></path>
                                        <path d="M3 20.9999L0 17.9999L3 14.9999L6 17.9999L3 20.9999Z" stroke="#292929" stroke-width="2.5" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_429_11113">
                                            <rect width="24" height="24" fill="white"></rect>
                                        </clipPath>
                                    </defs>
                                </svg>
                            </figure>
                            <div class="compo-item__title">
                                <h3>{{$c->name}}</h3>
                                <div class="btns">
                                    <a href="{{route('shuttle.user_component', $c->pivot->id)}}" class="btn-primary btn-edit">
                                        <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21.2799 6.40005L11.7399 15.94C10.7899 16.89 7.96987 17.33 7.33987 16.7C6.70987 16.07 7.13987 13.25 8.08987 12.3L17.6399 2.75002C17.8754 2.49308 18.1605 2.28654 18.4781 2.14284C18.7956 1.99914 19.139 1.92124 19.4875 1.9139C19.8359 1.90657 20.1823 1.96991 20.5056 2.10012C20.8289 2.23033 21.1225 2.42473 21.3686 2.67153C21.6147 2.91833 21.8083 3.21243 21.9376 3.53609C22.0669 3.85976 22.1294 4.20626 22.1211 4.55471C22.1128 4.90316 22.0339 5.24635 21.8894 5.5635C21.7448 5.88065 21.5375 6.16524 21.2799 6.40005V6.40005Z" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M11 4H6C4.93913 4 3.92178 4.42142 3.17163 5.17157C2.42149 5.92172 2 6.93913 2 8V18C2 19.0609 2.42149 20.0783 3.17163 20.8284C3.92178 21.5786 4.93913 22 6 22H16C17.0609 22 18.0783 21.5786 18.8284 20.8284C19.5786 20.0783 20 19.0609 20 18V13" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg> Edit
                                    </a>
                                    <!-- /.btn-primary -->
                                    <a href="{{route('shuttle.awwards.component.destroy', $c->pivot->id)}}" class="btn-primary btn-delete">
                                        <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M10 11V17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M14 11V17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M4 7H20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M6 7H12H18V18C18 19.6569 16.6569 21 15 21H9C7.34315 21 6 19.6569 6 18V7Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5V7H9V5Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg> Delete
                                    </a>
                                    <!-- /.btn-primary -->
                                </div>
                                <!-- /.btns -->
                            </div>
                            <!-- /.compo-item__title -->
                        </div>
                        <!-- /.compo-item -->
                    </div>
                </div>
                @endforeach
                @else
                <div class="col-md-12">
                    <p>No components added yet.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif


</div>
@stop

@push('js')
    <script>
        // Component management
        $( ".sortable" ).sortable({
            revert: true,
            update: function (event, ui) {
                let data = $(event.target).sortable("toArray");
                sortComponent(data);
            }
        });

        function sortComponent(data) {
            let frmData = new FormData();
            frmData.append('data',data);
            $.ajax({
                type: "post",
                url: "{{route('shuttle.component.sort')}}",
                data: {data: data},
                success: function () {
                    showNotification('top', 'right', "success", "Saved");
                }
            });
        }

        $("a.btn-delete").on('click',function (e) {
            e.preventDefault();
            let form = $('<form action="'+$(this).attr('href')+'" method="post">'+
                '@csrf'+'@method("DELETE")'+'</form>');
            form.appendTo('body');
            form.submit();
        });

        // Package management
        $('.addbtn').click(function(e) {
            e.preventDefault();
            addPlan();
        });

        $(document).on("click", '.trashBtn', function(e) {
            e.preventDefault();
            $(this).parents('tr').remove()
        })

        const uid = function(){
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function addPlan() {
            var id = uid();

            // Remove the "No packages yet" message if it exists
            $('#other-chance-ticket-table tr').each(function() {
                if ($(this).find('td[colspan="5"]').length > 0) {
                    $(this).remove();
                }
            });

            var $el = `<tr>
                        <td>
                            <input class="form-control" name="locations[${id}][title]" placeholder="Package Title" />
                        </td>
                        <td>
                            <input class="form-control" type="date" name="locations[${id}][start_at]" />
                        </td>
                        <td>
                            <input class="form-control" type="date" name="locations[${id}][end_at]" />
                        </td>
                        <td>
                            <input class="form-control" name="locations[${id}][price]" placeholder="0.00" />
                        </td>
                        <td class="text-right">
                            <button class="btn btn-danger icon-button trashBtn" type="button">
                                <i class="simple-icon-trash"></i>
                            </button>
                        </td>
                    </tr>`;

            $('#other-chance-ticket-table').append($el);

        }
    </script>
@endpush