<?php
// Check if files exist in correct locations
// Upload this file to your server root and visit it in browser

echo "<h2>File Check Results:</h2>";

// Check if edit_add.blade.php exists
$editAddFile = 'resources/views/shuttle/awwards/edit_add.blade.php';
if (file_exists($editAddFile)) {
    echo "✅ SUCCESS: {$editAddFile} exists!<br>";
    echo "File size: " . filesize($editAddFile) . " bytes<br>";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime($editAddFile)) . "<br>";
} else {
    echo "❌ ERROR: {$editAddFile} does NOT exist!<br>";
}

echo "<hr>";

// Check if directory exists
$dir = 'resources/views/shuttle/awwards/';
if (is_dir($dir)) {
    echo "✅ SUCCESS: Directory {$dir} exists!<br>";
    echo "Files in directory:<br>";
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo "- {$file}<br>";
        }
    }
} else {
    echo "❌ ERROR: Directory {$dir} does NOT exist!<br>";
}

echo "<hr>";

// Check if we can read the file content
if (file_exists($editAddFile)) {
    echo "<h3>File Content Preview (first 500 characters):</h3>";
    echo "<pre>" . htmlspecialchars(substr(file_get_contents($editAddFile), 0, 500)) . "...</pre>";
}
?>
