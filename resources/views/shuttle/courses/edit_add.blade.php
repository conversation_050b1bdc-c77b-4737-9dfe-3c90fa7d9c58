@extends('shuttle::admin')

@php $locationOptions = \App\Models\Location::all()->map(fn($loc) => '<option value="'.$loc->id.'">'.$loc->name.'</option>')->implode('') @endphp

@section('main')
<div class="row">
    <div class="col-md-12">
        <x-shuttle-form
                :action="$dataTypeContent->id ? route('shuttle.scaffold_interface.update', ['scaffold_interface' => $scaffold_interface, 'id' => $dataTypeContent->id, 'lang' => $lang]) : route('shuttle.scaffold_interface.store',$scaffold_interface)"
                :scaffold-interface-rows="$scaffold_interface->rows"
                :data-type-content="$dataTypeContent"
                :edit="$dataTypeContent->id ? true : false"
        >
            <x-slot name="course_hasmany_course_location_relationship">
                <div class="form-group col-md-12 " id="media_lib_app">
                    <table class="table table-striped table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th scope="col">Location</th>
                                <th scope="col">Start</th>
                                <th scope="col">End</th>
                                <th scope="col">Price</th>
                                <th class="text-right">
                                    <button class="btn btn-primary icon-button addbtn" type="button">
                                        <i class="simple-icon-plus"></i>
                                    </button>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="other-chance-ticket-table">
                            @foreach ($dataTypeContent->courseLocations ?? [] as $item)
                                <tr>
                                    <td>
                                        <input name="locations[{{$loop->index}}][id]" value="{{$item->id}}" hidden="">
                                        <select class="form-control country-select" name="locations[{{$loop->index}}][location_id]" data-value="{{$item->location_id}}">{!! $locationOptions !!}</select>
                                    </td>
                                    <td>
                                        <input class="form-control" name="locations[{{$loop->index}}][start_at]" type="date" value="{{$item->start_at?->format('Y-m-d')}}" />
                                    </td>
                                    <td>
                                        <input class="form-control" name="locations[{{$loop->index}}][end_at]" type="date" value="{{$item->end_at?->format('Y-m-d')}}" />
                                    </td>
                                    <td>
                                        <input class="form-control" name="locations[{{$loop->index}}][price]" value="{{$item->price}}" />
                                    </td>
                                    <td class="text-right">
                                        <button class="btn btn-danger icon-button trashBtn" type="button">
                                            <i class="simple-icon-plus"></i>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </x-slot>
        </x-shuttle-form>
    </div>
</div>
@stop

@push('js')
    <script>
        $(".country-select").each(function(item) {
            $(this).val($(this).data('value')).change()
        })

        $('.addbtn').click(function(e) {
            e.preventDefault();
            addPlan();
        });

        $(document).on("click", '.trashBtn', function(e) {
            e.preventDefault();
            $(this).parents('tr').remove()
        })

        const uid = function(){
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function addPlan() {
            var id = uid();
            var $el = `<tr>
                        <td>
                            <select class="form-control" name="locations[${id}][location_id]">{!! $locationOptions !!}</select>
                        </td>
                        <td>
                            <input class="form-control" type="date" name="locations[${id}][start_at]" />
                        </td>
                        <td>
                            <input class="form-control" type="date" name="locations[${id}][end_at]" />
                        </td>
                        <td>
                            <input class="form-control" name="locations[${id}][price]" />
                        </td>
                        <td class="text-right">
                            <button class="btn btn-danger icon-button trashBtn" type="button">
                                <i class="simple-icon-plus"></i>
                            </button>
                        </td>
                    </tr>`;

            $('#other-chance-ticket-table').append($el);

        }
    </script>
@endpush