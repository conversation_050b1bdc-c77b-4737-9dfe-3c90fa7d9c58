<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AwwardCategory extends Model
{
    protected $table = 'awwards_categories';

    protected $fillable = [
        'image',
        'title',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function awwards(): HasMany
    {
        return $this->hasMany(Awward::class, 'category_id');
    }
} 