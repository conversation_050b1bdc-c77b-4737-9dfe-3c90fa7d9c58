<?php

namespace Sina\Shuttle\FormFields;

class PasswordHandler extends <PERSON>bstract<PERSON><PERSON><PERSON>
{
    protected $codename = 'password';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.password', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
