@php $url = ""; if(isset($page) && $page->model == 'home'){ if(LaravelLocalization::getCurrentLocale() == 'ka') { $url = url('/'); } else { $url = LaravelLocalization::localizeUrl('/'); } } else { $url = url()->current(); } @endphp
@extends('app')
@push('seo')
    <title>{{($data && !is_array($data) && !is_a($data, 'Illuminate\Database\Eloquent\Collection') && !is_a($data, 'Illuminate\Pagination\LengthAwarePaginator')) ? $data->title : $page->title}} - Business-Eagles.com</title>
    <meta name="keywords" content="{{($data && !is_array($data)  && !is_a($data, 'Illuminate\Database\Eloquent\Collection') && !is_a($data, 'Illuminate\Pagination\LengthAwarePaginator')) ? $data->keywords : $page->keywords}}">
    <meta name="description" content="{{($data && !is_array($data) && !is_a($data, 'Illuminate\Database\Eloquent\Collection') && !is_a($data, 'Illuminate\Pagination\LengthAwarePaginator')) ? $data->description : $page->description}}">
    <link rel="canonical" href="{{$url}}" />
    <meta itemprop="name" content="{{$page->title}} - Business-Eagles.com">
    <meta itemprop="description" content="{{($data && !is_array($data) && !is_a($data, 'Illuminate\Database\Eloquent\Collection') && !is_a($data, 'Illuminate\Pagination\LengthAwarePaginator')) ? $data->description : $page->description}}">
    <meta itemprop="image" content="{{($data && isset($data->image)) ? url(Storage::url($data->image)) : url(Storage::url($page->image))}}">
    <meta property="fb:app_id" content="788287088369573" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="Business-Eagles.com" />
    <meta property="og:url" content="{{$url}}" />
    <meta property="og:title" content="{{($data && isset($data->title)) ? $data->title : $page->title}} - Business-Eagles.com" />
    <meta property="og:description" content="{{$page->description}}" />
    <meta property="og:image" content="{{($data && isset($data->image)) ? url(Storage::url($data->image)) : url(Storage::url($page->image))}}" />
    <meta name="geo.region" content="GE-TB" />
    <meta name="geo.placename" content="Tbilisi" />
    <meta name="geo.position" content="41.726687;44.744868" />
    <meta name="ICBM" content="41.726687, 44.744868" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{asset('css/apple-touch-icon.pn')}}g">
    <link rel="icon" type="image/png" sizes="32x32" href="{{asset('css/favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{asset('css/favicon-16x16.png')}}">
    <link rel="manifest" href="{{asset('css/site.webmanifest')}}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">
    <meta name="author" content="Shuttle.ge">
    <meta name="copyright" content="Shuttle.ge">
@endpush
@section('content')
    @include('includes.header')
    @if(optional($page->type)->name != 'home')<x-shuttle-breadcrumb :data="$data" :page="$page" view="includes.breadcrumb"></x-shuttle-breadcrumb>@endif
    @includeIf($include_file, ['data' => $data, 'route' => $route])
@stop
