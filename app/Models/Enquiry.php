<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Enquiry extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    /**
     * Default ordering by newest first
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('ordered', function ($builder) {
            $builder->orderBy('created_at', 'desc');
        });
    }

    public function model()
    {
        return $this->morphTo();
    }
}
