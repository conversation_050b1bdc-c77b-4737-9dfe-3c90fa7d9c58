<!DOCTYPE html>
<html>
<head>
    <title>Google Sign-In Test</title>
    <meta name="google-signin-client_id" content="{{ config('services.google.client_id') }}">
    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <h1>Google Sign-In Test (Plesk-friendly)</h1>

    <div id="status"></div>

    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0;">
        <strong>Debug Info:</strong><br>
        Google Client ID: {{ substr(config('services.google.client_id'), 0, 20) }}...<br>
        Current URL: {{ url()->current() }}<br>
        CSRF Token: {{ csrf_token() }}<br>
    </div>
    
    <!-- Google Sign-In Button (Old API) -->
    <div class="g-signin2" data-onsuccess="onSignIn" data-theme="dark" style="margin-bottom: 10px;"></div>

    <!-- Alternative Google Sign-In Button (New API) -->
    <div id="g_id_onload"
         data-client_id="{{ config('services.google.client_id') }}"
         data-callback="handleCredentialResponse"
         data-auto_prompt="false">
    </div>
    <div class="g_id_signin"
         data-type="standard"
         data-size="large"
         data-theme="outline"
         data-text="sign_in_with"
         data-shape="rectangular"
         data-logo_alignment="left">
    </div>

    <!-- Manual Button -->
    <br><br>
    <button id="manual-signin-btn" class="btn btn-primary" onclick="manualSignIn();" style="margin-right: 10px;">
        Manual Google Sign-In
    </button>
    <button onclick="signOut();">Sign Out</button>
    
    <script>
        // New Google Identity Services API callback
        function handleCredentialResponse(response) {
            console.log('New API - Credential response received');
            document.getElementById('status').innerHTML = 'Processing new API login...';

            // Send the credential to server
            fetch('/auth/google/simple', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    token: response.credential
                })
            })
            .then(response => {
                console.log('New API - Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('New API - Response text:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        document.getElementById('status').innerHTML = 'Success! Redirecting...';
                        window.location.href = data.redirect;
                    } else {
                        document.getElementById('status').innerHTML = 'Error: ' + (data.error || 'Unknown error');
                    }
                } catch (e) {
                    document.getElementById('status').innerHTML = 'Parse error: ' + e.message;
                }
            })
            .catch(error => {
                console.error('New API - Fetch error:', error);
                document.getElementById('status').innerHTML = 'Network error: ' + error;
            });
        }

        // Manual sign-in function
        function manualSignIn() {
            console.log('Manual sign-in triggered');
            document.getElementById('status').innerHTML = 'Initializing manual sign-in...';

            if (typeof gapi !== 'undefined' && gapi.auth2) {
                const authInstance = gapi.auth2.getAuthInstance();
                if (authInstance) {
                    authInstance.signIn().then(onSignIn);
                } else {
                    document.getElementById('status').innerHTML = 'Google Auth not initialized. Trying to initialize...';
                    gapi.load('auth2', function() {
                        gapi.auth2.init({
                            client_id: '{{ config('services.google.client_id') }}'
                        }).then(function() {
                            gapi.auth2.getAuthInstance().signIn().then(onSignIn);
                        });
                    });
                }
            } else {
                document.getElementById('status').innerHTML = 'Google API not loaded. Please refresh the page.';
            }
        }

        // Old API callback (legacy)
        function onSignIn(googleUser) {
            const profile = googleUser.getBasicProfile();
            const idToken = googleUser.getAuthResponse().id_token;
            
            document.getElementById('status').innerHTML = 'Signing in...';
            
            // Send token to server
            fetch('/auth/google/simple', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    token: idToken
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text(); // Get as text first to see what we're getting
            })
            .then(text => {
                console.log('Response text:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        document.getElementById('status').innerHTML = 'Success! Redirecting...';
                        window.location.href = data.redirect;
                    } else {
                        document.getElementById('status').innerHTML = 'Error: ' + (data.error || 'Unknown error');
                    }
                } catch (e) {
                    document.getElementById('status').innerHTML = 'Parse error: ' + e.message + '<br>Response: ' + text.substring(0, 500);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                document.getElementById('status').innerHTML = 'Network error: ' + error;
            });
        }
        
        function signOut() {
            if (typeof gapi !== 'undefined' && gapi.auth2) {
                const auth2 = gapi.auth2.getAuthInstance();
                if (auth2) {
                    auth2.signOut().then(function () {
                        document.getElementById('status').innerHTML = 'Signed out.';
                    });
                }
            }

            // Also try to sign out from new API
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.disableAutoSelect();
                document.getElementById('status').innerHTML = 'Signed out.';
            }
        }

        // Add click event listeners when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add retry mechanism for old API button
            const oldButton = document.querySelector('.g-signin2');
            if (oldButton) {
                let clickCount = 0;
                oldButton.addEventListener('click', function(e) {
                    clickCount++;
                    console.log('Google Sign-In button clicked, attempt:', clickCount);
                    document.getElementById('status').innerHTML = 'Attempting to sign in... (attempt ' + clickCount + ')';

                    // If multiple clicks, try manual approach
                    if (clickCount > 2) {
                        setTimeout(manualSignIn, 500);
                    }
                });
            }

            // Add status for new API
            const newButton = document.querySelector('.g_id_signin');
            if (newButton) {
                newButton.addEventListener('click', function() {
                    console.log('New Google Sign-In button clicked');
                    document.getElementById('status').innerHTML = 'Processing with new API...';
                });
            }
        });
    </script>
</body>
</html>
