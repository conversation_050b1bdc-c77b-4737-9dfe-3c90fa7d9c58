<?php

namespace Sina\Shuttle\FormFields;

class SvgHandler extends <PERSON>bstractHandler
{
    protected $codename = 'svg';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.text_area', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
