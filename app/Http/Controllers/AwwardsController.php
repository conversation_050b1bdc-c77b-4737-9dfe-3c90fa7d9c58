<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ConferenceCategory;
use App\Models\Location;
use App\Models\Client;
use App\Models\Course;
use App\Models\Awward;
use App\Models\AwwardCategory;
use App\Models\ConferencePackage;
use Illuminate\Http\Request;
use DB;

class AwwardsController extends Controller
{
    public function index(Request $request)
    {
        $clients = Client::all();

        $categories = AwwardCategory::withCount('awward')->get();
        $locations = Location::all();

        $awward = Awward::query(); 

        if(!empty($request->category_id)) {
            $awward = $awward->where('category_id', $request->category_id);
        }
    
        $awward = $awward->with('category')
            ->paginate(8);
    
        return view('awwards.index', compact( 'awward' , 'clients', 'locations', 'categories'));
    }

    public function show(Request $request)
{
    $title = preg_replace('/[^a-z0-9]/', '', $request->awward_title);
    $awward = Awward::where(DB::raw("LOWER(REGEXP_REPLACE(title, '[^0-9a-z]', ''))"), $title)
        ->with('packages')
        ->first();

    if (!$awward) {
        abort(404, 'Award not found');
    }

    // Create a default package if none exists
    if (!$awward->packages || $awward->packages->isEmpty()) {
        $awward->packages()->create([
            'title' => $awward->title,
            'price' => 0, // Default price, can be changed in admin
            'start_at' => now(),
            'end_at' => now()->addDays(1),
        ]);
        $awward->load('packages'); // Reload the relationship
    }

    $clients = Client::all();
    $categories = AwwardCategory::all();
    $title = $awward->title;

    // compact-იდანაც ამოვიღოთ 'upcomingPackages'
    return view('awwards.show', compact('awward', 'clients', 'categories', 'title'));
}

}