
/**
 * fileuploader
 * Copyright (c) 2020 Innostudio.de
 * Website: https://innostudio.de/fileuploader/
 * Version: 2.2 (12 Mar 2020)
 * License: https://innostudio.de/fileuploader/documentation/#license
 */

@charset "UTF-8";
.fileuploader-input .fileuploader-input-button,
.fileuploader-input .fileuploader-input-caption,
.fileuploader-items .fileuploader-item .fileuploader-action,
.fileuploader-items .fileuploader-item .fileuploader-progressbar .bar,
.fileuploader-items .fileuploader-item .progress-bar2 .fileuploader-progressbar .bar,
.fileuploader-items .fileuploader-item span.fileuploader-action-popup,
.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-tools li [data-action],
.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer button[data-action],
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button.button-success {
    -webkit-transition: .7s cubic-bezier(.17, .67, 0, 1.01);
    -o-transition: .7s cubic-bezier(.17, .67, 0, 1.01);
    transition: .7s cubic-bezier(.17, .67, 0, 1.01)
}

.fileuploader-items .fileuploader-item,
.fileuploader-items .fileuploader-item .fileuploader-action,
.fileuploader-items .fileuploader-item .fileuploader-item-icon,
.fileuploader-items .fileuploader-item .fileuploader-item-image canvas,
.fileuploader-items .fileuploader-item .fileuploader-item-image img,
.fileuploader-popup,
.fileuploader-popup .fileuploader-popup-node {
    -webkit-animation: fileuploaderFadeIn .2s ease;
    animation: fileuploaderFadeIn .2s ease
}

.fileuploader-input .fileuploader-input-button,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button.button-success {
    display: inline-block;
    margin: 0;
    padding: 14px 22px;
    border: none;
    border-radius: 30px;
    outline: 0;
    font-weight: 700;
    cursor: pointer;
    vertical-align: middle;
    text-decoration: none
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button {
    background: #e6ebf4;
    color: #90a0bc
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button:hover {
    background: #edf1f7;
    transform: translateY(-1px)
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button:active {
    background: #dfe5f1;
    transform: translateY(1px)
}

.fileuploader-input .fileuploader-input-button,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button.button-success {
    background: #9658fe;
    background: -moz-linear-gradient(-45deg, #3a8ffe 0, #9658fe 100%);
    background: -webkit-linear-gradient(-45deg, #3a8ffe 0, #9658fe 100%);
    background: linear-gradient(135deg, #3a8ffe 0, #9658fe 100%);
    background-size: 140% auto;
    background-position: center;
    color: #fff;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .04)
}

.fileuploader-input .fileuploader-input-button:hover,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button.button-success:hover {
    background-position: left;
    box-shadow: 0 8px 25px rgba(0, 0, 0, .15);
    transform: translateY(-2px)
}

.fileuploader-input .fileuploader-input-button:active,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button.button-success:active {
    background-position: right;
    box-shadow: 0 4px 25px rgba(0, 0, 0, .15);
    transform: translateY(2px)
}

.fileuploader-input .fileuploader-input-caption .fileuploader-pending-loader,
.fileuploader-items .fileuploader-item .fileuploader-item-image.fileuploader-loading:after,
.fileuploader-items .fileuploader-item.upload-pending .fileuploader-action-remove:after,
.fileuploader-popup.loading:after {
    content: "";
    position: absolute;
    min-width: 24px;
    min-height: 24px;
    max-width: 48px;
    max-height: 48px;
    background: url(data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJsb2FkZXItMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI0MHB4IiBoZWlnaHQ9IjQwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjZGRlNGY2IiBkPSJNNDMuOTM1LDI1LjE0NWMwLTEwLjMxOC04LjM2NC0xOC42ODMtMTguNjgzLTE4LjY4M2MtMTAuMzE4LDAtMTguNjgzLDguMzY1LTE4LjY4MywxOC42ODNoNC4wNjhjMC04LjA3MSw2LjU0My0xNC42MTUsMTQuNjE1LTE0LjYxNWM4LjA3MiwwLDE0LjYxNSw2LjU0MywxNC42MTUsMTQuNjE1SDQzLjkzNXoiPjxhbmltYXRlVHJhbnNmb3JtIGF0dHJpYnV0ZVR5cGU9InhtbCIgYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiB0eXBlPSJyb3RhdGUiIGZyb209IjAgMjUgMjUiIHRvPSIzNjAgMjUgMjUiIGR1cj0iMC42cyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiLz4gPC9wYXRoPiA8L3N2Zz4=) no-repeat center;
    background-size: contain
}

.fileuploader-items .fileuploader-item .fileuploader-item-icon i,
.fileuploader-items .fileuploader-item .fileuploader-item-image canvas,
.fileuploader-items .fileuploader-item .fileuploader-item-image img,
.fileuploader-items .fileuploader-item .fileuploader-item-image.fileuploader-loading:after,
.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point:after,
.fileuploader-popup .fileuploader-popup-move:after,
.fileuploader-popup.loading:after {
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%)
}

.fileuploader-items .fileuploader-item .fileuploader-action,
.fileuploader-items .fileuploader-item .progress-bar2 span {
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.fileuploader-input .fileuploader-input-button,
.fileuploader-items .fileuploader-item .fileuploader-item-icon,
.fileuploader-items .fileuploader-item .progress-bar2 .fileuploader-progressbar,
.fileuploader-popup .fileuploader-cropper,
.fileuploader-popup .fileuploader-cropper *,
.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-tools li [data-action],
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button,
.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button.button-success,
.fileuploader-popup .fileuploader-popup-move,
.fileuploader-popup .fileuploader-popup-node .fileuploader-popup-file-icon {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.fileuploader,
.fileuploader *,
.fileuploader :after,
.fileuploader :before,
.fileuploader-popup,
.fileuploader-popup *,
.fileuploader-popup :after,
.fileuploader-popup :before {
    box-sizing: border-box
}

.fileuploader,
.fileuploader-popup {
    font-family: Roboto, "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: normal;
    text-align: left
}

.fileuploader button,
.fileuploader-popup button {
    display: inline-block;
    padding: 0;
    margin: 0;
    border: 0;
    font: inherit;
    background: 0 0;
    box-shadow: none
}

.fileuploader button:focus,
.fileuploader-popup button:focus {
    outline: 0
}

.fileuploader {
    display: block;
    width: 100%;
    padding: 16px;
    margin: 16px 0;
    background: #fafbfd;
    border-radius: 6px
}

.fileuploader-icon-main {
    display: inline-block;
    font-size: 18px;
    color: #789bec
}

.fileuploader-input {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border: 1px solid transparent;
    border-radius: 30px;
    cursor: pointer
}

.fileuploader-input .fileuploader-input-caption {
    position: relative;
    display: inline-block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -ms-flex-item-align: start;
    align-self: flex-start;
    padding: 13px 16px;
    margin-right: 16px;
    background: #fff;
    border: 1px solid #ebeef1;
    border-radius: 30px;
    color: #789bec;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .01);
    font-weight: 700;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

.fileuploader-focused .fileuploader-input .fileuploader-input-caption {
    border-color: #789bec
}

.fileuploader-input .fileuploader-input-caption .fileuploader-pending-loader {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    min-height: 22px;
    margin-right: 6px
}

.fileuploader-input.fileuploader-dragging {
    background: #fff;
    border: 1px solid #ebeef1;
    border-style: dashed
}

.fileuploader-input.fileuploader-dragging .fileuploader-input-caption {
    border-color: transparent
}

.fileuploader-input.fileuploader-dragging .fileuploader-input-button {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    opacity: 0
}

.fileuploader-disabled .fileuploader-input {
    opacity: .7;
    cursor: default;
    pointer-events: none!important
}

.fileuploader-items .fileuploader-items-list {
    display: block;
    margin: 0 -16px;
    padding: 0;
    list-style: none
}

.fileuploader-items .fileuploader-item {
    position: relative;
    margin: 0;
    padding: 20px 16px 20px 22px;
    border-bottom: 1px solid #ebeef1;
    animation-duration: .6s
}

.fileuploader-items .fileuploader-item:last-child {
    border-bottom: 0;
    margin-bottom: -16px
}

.fileuploader-items .fileuploader-item.upload-failed {
    background: rgba(254, 84, 111, .06)
}

.fileuploader-items .fileuploader-item.upload-pending .fileuploader-action-remove:after {
    position: absolute;
    left: -8px;
    top: -8px;
    width: 36px;
    height: 36px
}

.fileuploader-items .fileuploader-item .columns {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    z-index: 2
}

.fileuploader-items .fileuploader-item .column-thumbnail {
    position: relative;
    width: 36px;
    height: 36px
}

.fileuploader-items .fileuploader-item .column-title {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 16px;
    padding-right: 16px;
    color: #74809d;
    overflow: hidden
}

.fileuploader-items .fileuploader-item .column-title a {
    display: inline-block;
    width: 100%;
    height: 100%;
    color: #74809d;
    text-decoration: none
}

.fileuploader-items .fileuploader-item .column-title div {
    width: 100%;
    font-weight: 700;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.fileuploader-items .fileuploader-item .column-title span {
    font-size: 12px;
    color: #979fb8
}

.fileuploader-items .fileuploader-item .column-actions {
    margin: 0 16px
}

.fileuploader-items .fileuploader-item .fileuploader-item-image {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden
}

.fileuploader-items .fileuploader-item .fileuploader-item-image.fileuploader-loading:after {
    content: "";
    width: 50%;
    height: 50%
}

.fileuploader-items .fileuploader-item .fileuploader-item-image canvas,
.fileuploader-items .fileuploader-item .fileuploader-item-image img {
    position: absolute;
    max-width: none;
    max-height: 100%;
    background: #fff
}

.fileuploader-items .fileuploader-item .fileuploader-item-icon {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    color: #fff;
    font-size: 11px;
    border-radius: 4px;
    cursor: default;
    background-color: #ddd;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 28px
}

.fileuploader-items .fileuploader-item .fileuploader-item-icon.is-bright-color {
    color: #888
}

.fileuploader-items .fileuploader-item .fileuploader-item-icon i {
    position: absolute;
    display: block;
    width: 90%;
    font-style: normal;
    font-weight: 700;
    overflow: hidden;
    white-space: nowrap
}

.fileuploader-items .fileuploader-item span.fileuploader-action-popup {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(43, 56, 71, .2);
    border-radius: 6px;
    cursor: pointer;
    opacity: 0
}

.fileuploader-items .fileuploader-item span.fileuploader-action-popup:hover {
    opacity: 1
}

.fileuploader-items .fileuploader-item span.fileuploader-action-popup:active {
    background: rgba(43, 56, 71, .4)
}

.fileuploader-items .fileuploader-item .fileuploader-action {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    color: #c0c6d2;
    cursor: pointer;
    vertical-align: top;
    text-align: center
}

.fileuploader-items .fileuploader-item .fileuploader-action+.fileuploader-action {
    margin-left: 16px
}

.fileuploader-items .fileuploader-item .fileuploader-action:hover {
    color: #789bec
}

.fileuploader-items .fileuploader-item .fileuploader-action:active {
    color: #668ee9
}

.fileuploader-items .fileuploader-item .fileuploader-action i {
    width: 100%;
    height: 100%;
    font-size: 20px;
    line-height: 20px
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove {
    color: #fff;
    background: #fe7676;
    border-radius: 50%;
    box-shadow: -1px 1px 6px rgba(254, 118, 118, .8)
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove i {
    font-size: 14px;
    text-shadow: none
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove:hover {
    background-color: #fe8a8a
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove:active {
    background-color: #fe6262
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-success {
    color: #fff;
    background: #43d084;
    border-radius: 50%;
    box-shadow: -1px 1px 6px rgba(67, 208, 132, .8)
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-success i {
    font-size: 14px;
    text-shadow: none
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-success:hover {
    background-color: #53d48f
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-success:active {
    background-color: #33cc79
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove.fileuploader-action-success i:before {
    content: ""
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove.fileuploader-action-success:active,
.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove.fileuploader-action-success:hover {
    background: #fe7676;
    box-shadow: -1px 1px 6px rgba(254, 118, 118, .8)
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove.fileuploader-action-success:active i:before,
.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove.fileuploader-action-success:hover i:before {
    content: ""
}

.fileuploader-items .fileuploader-item .fileuploader-action.fileuploader-action-remove.fileuploader-action-success:active {
    background-color: #fe6262
}

.fileuploader-items .fileuploader-item.file-has-popup span.fileuploader-action-popup {
    display: block
}

.fileuploader-items .fileuploader-item .fileuploader-progressbar {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4px;
    border-radius: 6px
}

.fileuploader-items .fileuploader-item .fileuploader-progressbar .bar {
    position: absolute;
    left: 0;
    top: 0;
    width: 0%;
    height: 100%;
    border-radius: 6px;
    background: #789bec;
    box-shadow: 0 4px 8px -1px rgba(120, 155, 236, .6)
}

.fileuploader-items .fileuploader-item .progress-bar2 .fileuploader-progressbar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1
}

.fileuploader-items .fileuploader-item .progress-bar2 .fileuploader-progressbar .bar {
    position: absolute;
    left: 0;
    top: 0;
    width: 0%;
    height: 100%;
    border-radius: 0;
    background: rgba(104, 125, 219, .08);
    box-shadow: none
}

.fileuploader-items .fileuploader-item .progress-bar2 span {
    position: absolute;
    right: 16px;
    color: rgba(151, 159, 184, .16);
    font-size: 48px
}

.fileuploader-items .fileuploader-item.sorting {
    background: #fafbfd;
    border-radius: 6px;
    opacity: .8;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    z-index: 799
}

.fileuploader-items .fileuploader-item.sorting,
.fileuploader-items .fileuploader-item.sorting .fileuploader-action,
.fileuploader-items .fileuploader-item.sorting .fileuploader-item-icon,
.fileuploader-items .fileuploader-item.sorting .fileuploader-item-image canvas,
.fileuploader-items .fileuploader-item.sorting .fileuploader-item-image img {
    -webkit-animation: none;
    animation: none
}

.fileuploader-items .fileuploader-sorter-placeholder {
    background: rgba(0, 0, 0, .03);
    margin: 0;
    padding: 0;
    -webkit-animation: none;
    animation: none
}

.file-type-image .fileuploader-item-icon {
    background-color: #3982fe!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M7,8.5C7,7.119,8.119,6,9.5,6S12,7.119,12,8.5S10.881,11,9.5,11S7,9.881,7,8.5z M14.5,11l-4,6l-2-3L5,19h15L14.5,11z'/%3E%3C/svg%3E")
}

.file-type-image .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-type-audio .fileuploader-item-icon {
    background-color: #66d043!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M13.21,5h-1.07v9.613c-0.685-0.381-1.62-0.504-2.58-0.271c-1.687,0.405-2.812,1.753-2.511,3.007c0.3,1.254,1.913,1.939,3.6,1.533c1.544-0.369,2.615-1.527,2.558-2.682h0.003V8.34c1.752,1.296,3.29,1.123,3.575,4.21C20.188,7.362,13.354,7.498,13.21,5z'/%3E%3C/svg%3E")
}

.file-type-audio .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-type-video .fileuploader-item-icon {
    background-color: #9868ff!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M16.81 11.28L8.487 6.107a.622.642 0 0 0-.326-.1c-.326 0-.592.28-.592.623h-.003l.003 10.738c0 .344.266.623.592.623.123 0 .225-.044.335-.106l8.315-5.166a.91.94 0 0 0 .323-.72.96.96 0 0 0-.323-.721z'/%3E%3C/svg%3E")
}

.file-type-video .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-rar .fileuploader-item-icon,
.file-ext-zip .fileuploader-item-icon {
    background-color: #ffd236!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M6,11h12v2H6V11z M6,15h8v2H6V15z M6,7h12v2H6V7z'/%3E%3C/svg%3E")
}

.file-ext-rar .fileuploader-item-icon i,
.file-ext-zip .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-pdf .fileuploader-item-icon {
    background-color: #ef5350!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M17.61 13.224c-.336-.115-.752-.16-1.242-.15l-.84.06-.952.158-.906-.958c-.662-.808-1.23-1.684-1.732-2.6l.257-.608.283-.825c.153-.528.227-.985.192-1.37-.117-1.353-.86-2.218-1.9-2.127S9.164 5.88 9.28 7.23c.03.354.16.752.37 1.196a8.11 8.11 0 0 0 .396.743l.56.846-.132.35-1.12 2.846-.705 1.628-.068.012-.797.17-.838.24c-.52.178-.937.38-1.232.63-1.04.87-1.324 1.978-.658 2.77s1.807.707 2.848-.164c.272-.23.523-.563.77-.988a8.87 8.87 0 0 0 .381-.75c.078-.17.137-.35.207-.522l.173-.364 3.614-1 1.18-.256.47.502.64.595c.42.354.808.606 1.174.733 1.283.442 2.376.115 2.712-.862s-.326-1.917-1.6-2.36zM10.88 5.94c.314-.028.595.3.663 ***********-.034.546-.15.95l-.263.79-.454-.83c-.156-.333-.248-.613-.265-.807-.068-.79.154-1.162.47-1.2zM7.683 16.947c-.183.32-.36.555-.5.68-.606.508-1.04.54-1.242.298s-.096-.66.51-1.168c.166-.14.467-.286.864-.42l.8-.24-.423.85zm5.104-3.19l-2.74.735.353-.847.193-.475.807-2.082c.417.673.878 1.344 1.4 1.976l.5.58-.524.114zm5.35 1.452c-.103.298-.517.422-1.265.163-.203-.07-.484-.254-.805-.524l-.617-.562.947-.075c.367-.01.66.022.844.086.748.258.998.612.896.912z'/%3E%3C/svg%3E")
}

.file-ext-pdf .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-doc .fileuploader-item-icon,
.file-ext-docx .fileuploader-item-icon,
.file-ext-rtf .fileuploader-item-icon {
    background-color: #2372ba!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M5 17.33V6.67L15 4v16L5 17.33zM7.974 8.5h-1.33l.922 7h1.708l.73-5.22.702 5.22h1.714l.938-7h-1.26l-.645 5.613L10.72 8.5h-1.4l-.77 5.613L7.974 8.5zM19 6h-3v12h3V6z'/%3E%3C/svg%3E")
}

.file-ext-doc .fileuploader-item-icon i,
.file-ext-docx .fileuploader-item-icon i,
.file-ext-rtf .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-xls .fileuploader-item-icon,
.file-ext-xlsx .fileuploader-item-icon {
    background-color: #14a73c!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M5 17.33V6.67L15 4v16L5 17.33zM19 6h-3v12h3V6zm-6.148 9.5l-2.08-3.5 2.043-3.5H11.57l-1.244 2.246c-.047.196-.125.382-.232.554-.088-.173-.158-.354-.21-.54l-1.2-2.26H7.338L9.33 12l-2.182 3.5h1.338l1.396-2.416c.066-.14.117-.385.14-.385a1.58 1.58 0 0 1 .131.385l1.38 2.416h1.32z'/%3E%3C/svg%3E")
}

.file-ext-xls .fileuploader-item-icon i,
.file-ext-xlsx .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-pps .fileuploader-item-icon,
.file-ext-ppsx .fileuploader-item-icon,
.file-ext-ppt .fileuploader-item-icon,
.file-ext-pptx .fileuploader-item-icon {
    background-color: #f26522!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M11,13h6.975c-0.256,3.355-3.054,6-6.475,6C7.91,19,5,16.09,5,12.5c0-3.421,2.645-6.219,6-6.475V13zM13,5.025V11h5.975C18.731,7.811,16.189,5.269,13,5.025z'/%3E%3C/svg%3E")
}

.file-ext-pps .fileuploader-item-icon i,
.file-ext-ppsx .fileuploader-item-icon i,
.file-ext-ppt .fileuploader-item-icon i,
.file-ext-pptx .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-psd .fileuploader-item-icon {
    background-color: #3172eb!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M7.853 11.374h.61c.57 0 .997-.112 1.28-.338s.424-.553.424-.983c0-.435-.12-.755-.356-.962S9.2 8.78 8.695 8.78h-.842v2.595zm4.183-1.387c0 .94-.293 1.66-.88 2.157s-1.424.747-2.507.747h-.796V16H6V7.262h2.79c1.06 0 1.867.228 2.417.683s.83 1.137.828 2.042zM18 14.016c0 .686-.238 1.207-.714 1.565s-1.188.538-2.137.538a6.63 6.63 0 0 1-1.243-.098c-.33-.063-.652-.16-.96-.29v-1.506c.367.17.75.303 1.144.4a4.66 4.66 0 0 0 1.122.161c.66 0 .99-.2.99-.573.005-.13-.042-.256-.13-.35a1.93 1.93 0 0 0-.454-.305c-.214-.112-.5-.244-.86-.397-.514-.215-.892-.414-1.133-.597-.225-.164-.405-.38-.526-.63-.11-.24-.163-.53-.163-.877 0-.594.23-1.053.69-1.377s1.112-.487 1.958-.487c.804 0 1.588.175 2.35.525l-.552 1.315c-.307-.134-.62-.25-.938-.353-.287-.092-.588-.138-.89-.138-.54 0-.807.146-.807.437 0 .163.085.305.26.424s.552.297 1.14.532c.52.21.904.408 1.147.592s.422.395.537.633.173.527.173.858z'/%3E%3C/svg%3E")
}

.file-ext-psd .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-ai .fileuploader-item-icon {
    background-color: #ff9e00!important;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M12.58 16l-.6-2.072H8.964L8.364 16h-1.89l2.922-8.738h2.145L14.473 16H12.58zm-1.02-3.618l-.937-3.185-.15-.582-1.07 3.767h2.155zm3.452-4.756c0-.59.328-.886.986-.886s.986.294.986.886c0 .282-.078.502-.244.656-.164.16-.412.238-.742.238-.658 0-.986-.298-.986-.894zM16.908 16h-1.816V9.347h1.816V16z'/%3E%3C/svg%3E")
}

.file-ext-ai .fileuploader-item-icon i {
    visibility: hidden;
    color: #fff
}

.file-ext-txt .fileuploader-item-icon {
    background-color: #454545!important
}

.file-ext-txt .fileuploader-item-icon i {
    color: #fff!important
}

.file-ext-css .fileuploader-item-icon {
    background-color: #26a69a!important
}

.file-ext-css .fileuploader-item-icon i {
    color: #fff!important
}

.file-ext-html .fileuploader-item-icon {
    background-color: #cf33a8!important
}

.file-ext-html .fileuploader-item-icon i {
    color: #fff!important
}

.fileuploader-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #191d1e;
    z-index: 1090;
    animation-duration: .4s
}

.fileuploader-popup.loading:after {
    width: 48px;
    height: 48px;
    z-index: 8
}

.fileuploader-popup .fileuploader-popup-preview {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
    z-index: 2
}

.fileuploader-popup .fileuploader-popup-node {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 100%;
    height: 100%;
    min-width: 20px;
    min-height: 20px;
    padding: 80px 56px 64px;
    overflow: hidden
}

.fileuploader-popup .fileuploader-popup-node.is-zoomed {
    display: block
}

.fileuploader-popup .fileuploader-popup-node .reader-node {
    position: relative;
    max-width: 100%;
    max-height: 100%;
    text-align: center;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.fileuploader-popup .fileuploader-popup-node .reader-node.is-movable {
    cursor: grab
}

.fileuploader-popup .fileuploader-popup-node .reader-node.is-amoving:hover,
.fileuploader-popup .fileuploader-popup-node .reader-node.is-moving {
    cursor: grabbing
}

.fileuploader-popup .fileuploader-popup-node .reader-node.is-amoving:hover .area-move,
.fileuploader-popup .fileuploader-popup-node .reader-node.is-amoving:hover .point,
.fileuploader-popup .fileuploader-popup-node .reader-node.is-moving .area-move,
.fileuploader-popup .fileuploader-popup-node .reader-node.is-moving .point {
    cursor: grabbing!important
}

.fileuploader-popup .fileuploader-popup-node.node-astext .reader-node>div,
.fileuploader-popup .fileuploader-popup-node.node-audio .reader-node>audio,
.fileuploader-popup .fileuploader-popup-node.node-image .reader-node>img,
.fileuploader-popup .fileuploader-popup-node.node-video .reader-node>video {
    width: auto;
    max-width: 100%;
    max-height: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    color: #47525d;
    background: #fafafa;
    box-shadow: 0 0 18px rgba(0, 0, 0, .4);
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    border-radius: 0;
    outline: 0
}

.fileuploader-popup .fileuploader-popup-node.node-audio .reader-node audio {
    width: 450px;
    border-radius: 34px
}

.fileuploader-popup .fileuploader-popup-node.node-application .reader-node iframe {
    width: 100%;
    height: 100%;
    border: 0;
    border-radius: 3px
}

.fileuploader-popup .fileuploader-popup-node.node-astext .reader-node div {
    max-width: 992px;
    padding: 20px;
    margin: 0 auto;
    font-size: 14px;
    line-height: 16px;
    text-align: left;
    overflow-y: auto;
    white-space: pre-wrap
}

.fileuploader-popup .fileuploader-popup-node.has-node-centered {
    display: block
}

.fileuploader-popup .fileuploader-popup-node.has-node-centered .reader-node {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%
}

.fileuploader-popup .fileuploader-popup-node .fileuploader-popup-file-icon {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 80px;
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDkiIGhlaWdodD0iNjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgcHJlc2VydmVBc3BlY3RSYXRpbz0ieE1pZFlNaWQiPiA8Zz4gIDx0aXRsZT5iYWNrZ3JvdW5kPC90aXRsZT4gIDxyZWN0IGZpbGw9Im5vbmUiIGlkPSJjYW52YXNfYmFja2dyb3VuZCIgaGVpZ2h0PSI0MDIiIHdpZHRoPSI1ODIiIHk9Ii0xIiB4PSItMSIvPiA8L2c+IDxnPiAgPHRpdGxlPkxheWVyIDE8L3RpdGxlPiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBmaWxsPSIjYmJjYWNmIiBpZD0ic3ZnXzIiIGQ9Im00OSwxNi44NDJsMCw0Mi4xMDVjMCwyLjc5MSAtMi4yNyw1LjA1MyAtNS4wNjksNS4wNTNsLTM4Ljg2MiwwYy0yLjgsMCAtNS4wNjksLTIuMjYyIC01LjA2OSwtNS4wNTNsMCwtNTMuODk0YzAsLTIuNzkxIDIuMjY5LC01LjA1MyA1LjA2OSwtNS4wNTNsMjcuMDM0LDBsMTYuODk3LDE2Ljg0MnoiIGNsYXNzPSJjbHMtMSIvPiAgPHBhdGggZmlsbD0iI2RmZWFlZSIgZmlsbC1ydWxlPSJldmVub2RkIiBpZD0ic3ZnXzQiIGQ9Im00OSwxNS44OTlsMCwyLjA5NmwtMTMuODEzLDBjLTIuODYsMCAtNC4xNzksLTIuMzIgLTQuMTc5LC01LjE4MWwwLC0xMi44MTRsMi4wOTIsMGwxNS45LDE1Ljg5OXoiIGNsYXNzPSJjbHMtMyIvPiA8L2c+PC9zdmc+) no-repeat center;
    background-size: contain
}

.fileuploader-popup .fileuploader-popup-node .fileuploader-popup-file-icon div {
    position: absolute;
    bottom: 14px;
    left: 0;
    padding: 4px 6px;
    border-radius: 4px;
    color: #fff;
    max-width: 100%;
    background-image: none;
    word-wrap: break-word
}

.fileuploader-popup .fileuploader-popup-node .fileuploader-popup-file-icon div.is-bright-color {
    color: #888
}

.fileuploader-popup .fileuploader-popup-node .fileuploader-popup-file-icon div i {
    text-transform: uppercase;
    font-style: normal;
    font-weight: 700;
    white-space: nowrap;
    visibility: visible
}

.fileuploader-popup .fileuploader-popup-content {
    color: #fdfdfd;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, .4)
}

.fileuploader-popup .fileuploader-popup-content ul {
    list-style: none;
    margin: 0;
    padding: 0
}

.fileuploader-popup .fileuploader-popup-content ul li {
    display: inline-block;
    padding: 0;
    margin: 0
}

.fileuploader-popup .fileuploader-popup-content ul li+li {
    margin-left: 16px
}

.fileuploader-popup .fileuploader-popup-header {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    padding: 16px;
    background: rgba(0, 0, 0, .8);
    background: -moz-linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .8) 100%);
    background: -webkit-linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .8) 100%);
    background: linear-gradient(360deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .8) 100%);
    z-index: 2
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-meta {
    display: flex;
    flex: 1;
    white-space: nowrap;
    overflow: hidden
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-meta li:first-child {
    overflow: hidden
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-meta span {
    display: block;
    color: #80868b;
    font-size: 14px
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-meta h5 {
    max-width: 100%;
    margin: 4px 0 0;
    font-size: 14px;
    font-weight: 700;
    text-overflow: ellipsis;
    overflow: hidden
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-info:not(:empty) {
    flex: 1;
    margin-left: 16px
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-buttons {
    margin-left: 16px
}

.fileuploader-popup .fileuploader-popup-header .fileuploader-popup-button {
    padding: 14px 24px;
    text-shadow: none
}

.fileuploader-popup .fileuploader-popup-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    background: rgba(0, 0, 0, .8);
    background: -moz-linear-gradient(0deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .8) 100%);
    background: -webkit-linear-gradient(0deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .8) 100%);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .8) 100%);
    z-index: 2
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-tools li [data-action] {
    display: inline-block;
    padding: 16px;
    padding-bottom: 13px;
    cursor: pointer;
    text-decoration: none;
    color: #fdfdfd;
    border-bottom: 3px solid transparent
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-tools li [data-action] i {
    display: inline-block;
    font-size: 18px;
    margin-top: -4px;
    margin-right: 6px;
    vertical-align: middle
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-tools li [data-action]:hover {
    border-bottom-color: #789bec;
    color: #fff
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer {
    font-size: 14px
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer button[data-action] {
    width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    border: none;
    border-radius: 50%;
    padding: 0;
    vertical-align: middle;
    color: #fdfdfd;
    background: rgba(255, 255, 255, .1);
    text-shadow: none
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer button[data-action]:hover {
    background: rgba(255, 255, 255, .3)
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer input {
    display: inline-block;
    -webkit-appearance: none;
    width: 130px;
    padding: 0;
    margin: 0 16px;
    vertical-align: middle;
    background: 0 0
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer input:focus {
    outline: 0
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer input::-webkit-slider-runnable-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    animate: .2s;
    box-shadow: none;
    background: #789bec;
    border-radius: 6px
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer input::-webkit-slider-thumb {
    height: 14px;
    width: 14px;
    border-radius: 50%;
    border: 0;
    background: #fff;
    cursor: pointer;
    -webkit-appearance: none;
    margin-top: -5px;
    box-shadow: 2px 2px 8px rgba(0, 0, 0, .8)
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer input::-moz-range-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    animate: .2s;
    box-shadow: none;
    background: #789bec;
    border-radius: 6px
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer input::-moz-range-thumb {
    height: 14px;
    width: 14px;
    border-radius: 50%;
    border: 0;
    background: #fff;
    cursor: pointer;
    -webkit-appearance: none;
    margin-top: -5px;
    box-shadow: 2px 2px 8px rgba(0, 0, 0, .8)
}

.fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer span {
    display: inline-block;
    min-width: 40px;
    text-align: center;
    margin-left: 6px;
    color: #fff;
    vertical-align: middle
}

.fileuploader-popup .fileuploader-popup-move {
    position: absolute;
    display: none;
    width: 56px;
    height: 100%;
    font-size: 24px;
    bottom: 0;
    left: 0;
    color: #fff;
    opacity: .4;
    cursor: pointer;
    z-index: 1
}

.fileuploader-popup .fileuploader-popup-move:hover {
    opacity: 1
}

.fileuploader-popup .fileuploader-popup-move:after {
    position: absolute
}

.fileuploader-popup .fileuploader-popup-move[data-action=next] {
    left: auto;
    right: 0
}

.fileuploader-popup .fileuploader-popup-has-arrows .fileuploader-popup-move {
    display: inline-block
}

.fileuploader-popup .fileuploader-cropper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 20, 27, .65);
    z-index: 9
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area {
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 0;
    z-index: 2;
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0)
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.has-grid:after,
.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.has-grid:before {
    content: "";
    position: absolute;
    border: 1px solid rgba(250, 250, 250, .8);
    opacity: 0;
    -webkit-transition: all .4s ease;
    transition: all .4s ease;
    z-index: 1
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.has-grid:before {
    top: 0;
    left: 50%;
    height: 100%;
    width: 34%;
    border-top: 0;
    border-bottom: 0;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.has-grid:after {
    top: 50%;
    left: 0;
    height: 34%;
    width: 100%;
    border-left: 0;
    border-right: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.has-grid.moving:after,
.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.has-grid.moving:before {
    opacity: 1
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point {
    position: absolute;
    width: 24px;
    height: 24px;
    z-index: 3
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point:after {
    content: "";
    width: 12px;
    height: 12px;
    position: absolute;
    background: #fff;
    box-shadow: 0 0 6px rgba(0, 0, 0, .4);
    border-radius: 50%
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-a {
    top: -12px;
    left: -12px;
    cursor: nw-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-b {
    top: -12px;
    left: 50%;
    margin-left: -12px;
    cursor: n-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-c {
    top: -12px;
    right: -12px;
    cursor: ne-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-d {
    top: 50%;
    right: -12px;
    margin-top: -12px;
    cursor: w-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-e {
    bottom: -12px;
    right: -12px;
    cursor: nw-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-f {
    bottom: -12px;
    left: 50%;
    margin-left: -12px;
    cursor: s-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-g {
    bottom: -12px;
    left: -12px;
    cursor: sw-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .point.point-h {
    left: -12px;
    top: 50%;
    margin-top: -12px;
    cursor: w-resize
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .area-move {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
    cursor: move
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .area-move:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, .8)
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .area-image {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .area-image img {
    width: auto;
    height: auto;
    max-width: none;
    max-height: none;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-transform-origin: top left;
    transform-origin: top left
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area .area-info {
    position: absolute;
    bottom: -12px;
    left: 50%;
    color: #fff;
    font-family: sans-serif;
    line-height: 1;
    font-size: 12px;
    text-align: center;
    padding: 4px 8px;
    background: rgba(0, 0, 0, .6);
    border-radius: 14px;
    white-space: nowrap;
    opacity: 0;
    -webkit-transform: translateX(-50%) translateY(100%);
    transform: translateX(-50%) translateY(100%);
    -webkit-transition: all .4s ease;
    transition: all .4s ease;
    z-index: 2
}

.fileuploader-popup .fileuploader-cropper .fileuploader-cropper-area.show-info .area-info {
    opacity: 0
}

@media all and (max-width:768px) {
    .fileuploader-popup .fileuploader-popup-header {
        display: block;
        padding: 0
    }
    .fileuploader-popup .fileuploader-popup-header .fileuploader-popup-meta {
        padding: 16px
    }
    .fileuploader-popup .fileuploader-popup-header .fileuploader-popup-buttons {
        position: fixed;
        left: 0;
        bottom: 16px;
        width: 100%;
        margin: 0;
        text-align: center
    }
    .fileuploader-popup .fileuploader-popup-node {
        padding-left: 16px;
        padding-right: 16px;
        padding-bottom: 117px
    }
    .fileuploader-popup .fileuploader-popup-footer {
        bottom: 61px;
        background: 0 0
    }
    .fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-zoomer {
        display: none
    }
    .fileuploader-popup .fileuploader-popup-footer .fileuploader-popup-tools li a:hover {
        border-color: transparent
    }
    .fileuploader-popup .fileuploader-popup-move {
        width: 30px
    }
    .fileuploader-popup .fileuploader-popup-has-arrows .fileuploader-popup-node {
        padding-left: 30px;
        padding-right: 30px
    }
}

@-webkit-keyframes fileuploaderFadeIn {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes fileuploaderFadeIn {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

.fileuploader-theme-dragdrop .fileuploader-input {
    display: block;
    padding: 60px 0;
    background: #fff;
    border: 2px dashed #c2cdda;
    border-radius: 14px;
    text-align: center;
}

.fileuploader-theme-dragdrop .fileuploader-input .fileuploader-icon-main {
    display: block;
    font-size: 56px;
    margin: 0 auto;
    margin-bottom: 26px;
}

.fileuploader-theme-dragdrop .fileuploader-input h3 {
    margin: 0;
    margin-bottom: 8px;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
    font-size: 18px;
    font-weight: bold;
    color: #5B5B7B;
    white-space: normal;
    box-shadow: none;
}

.fileuploader-theme-dragdrop .fileuploader-input p {
    margin: 0;
    padding: 0;
    color: #90a0bc;
    margin-bottom: 12px;
}

.fileuploader-theme-dragdrop .fileuploader-input .fileuploader-input-inner>* {
    -webkit-transition: 500ms cubic-bezier(0.17, 0.67, 0, 1.01);
    transition: 500ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.fileuploader-theme-dragdrop .fileuploader-input.fileuploader-dragging .fileuploader-input-inner>* {
    transform: translateY(18px);
    opacity: 0;
}

.fileuploader-theme-dragdrop .fileuploader-input.fileuploader-dragging .fileuploader-icon-main {
    transform: translateY(30px) scale(1.2);
    opacity: 0.6;
}

.fileuploader-theme-dragdrop .fileuploader-input.fileuploader-dragging .fileuploader-input-caption {
    transform: translateY(30px);
    opacity: 0.6;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input,
.fileuploader-theme-thumbnails .fileuploader-items-list .fileuploader-item {
    position: relative;
    display: inline-block;
    margin: 16px 0 0 16px;
    padding: 0;
    vertical-align: top;
    width: 25%;
    width: calc(25% - 16px);
    padding-top: 20%;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input-inner,
.fileuploader-theme-thumbnails .fileuploader-item-inner {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 6px;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input-inner {
    background: #e6ebf4;
    border: 2px dashed #92a7bf;
    text-align: center;
    font-size: 30px;
    color: #90a0bc;
    cursor: pointer;
    opacity: 0.5;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input-inner:hover {
    opacity: 1;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input-inner:active,
.fileuploader-theme-thumbnails .fileuploader-dragging .fileuploader-thumbnails-input-inner {
    background: #f6f6fb;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input-inner i {
    position: absolute;
    font-style: normal;
    left: 0;
    top: 0;
    top: 50%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-items-list {
    margin: -16px 0 0 -16px;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item {
    border-bottom: 0;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item:last-child {
    margin-bottom: 0;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item-inner {
    background: rgba(0, 0, 0, 0.02);
    overflow: hidden;
    z-index: 1;
}

.fileuploader-theme-thumbnails .fileuploader-item-inner .thumbnail-holder,
.fileuploader-theme-thumbnails .fileuploader-items-list .fileuploader-item-image {
    width: 100%;
    height: 100%;
}

.fileuploader-theme-thumbnails .fileuploader-items-list .fileuploader-item-image {
    position: relative;
    background: #ffff;
    text-align: center;
    overflow: hidden;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item .fileuploader-item-icon {
    background-size: 30%;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item .fileuploader-item-icon i {
    display: none;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item .fileuploader-action-popup {
    border-radius: 6px;
    z-index: 1;
}

.fileuploader-theme-thumbnails .fileuploader-item .type-holder {
    position: absolute;
    top: 6px;
    left: 6px;
    padding: 4px 6px;
    background: rgba(0, 0, 0, 0.4);
    text-transform: uppercase;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
    z-index: 2;
}

.fileuploader-theme-thumbnails .fileuploader-item .actions-holder {
    position: absolute;
    top: 6px;
    right: 6px;
    z-index: 2;
    height: 20px;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item .fileuploader-action {
    color: #fff;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item .fileuploader-action+.fileuploader-action {
    margin-left: 8px;
}

.fileuploader-theme-thumbnails .fileuploader-item .content-holder {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 6px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
    background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
    z-index: 2;
}

.fileuploader-theme-thumbnails .fileuploader-item .content-holder h5 {
    display: block;
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.fileuploader-theme-thumbnails .fileuploader-item .content-holder span {
    display: block;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item.upload-failed .fileuploader-item-inner {
    background: #db6868;
}

.fileuploader-theme-thumbnails .fileuploader-item .progress-holder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: none;
    z-index: 1;
}

.fileuploader-theme-thumbnails .fileuploader-item .progress-holder .fileuploader-progressbar {
    position: relative;
    top: 50%;
    width: 80%;
    height: 6px;
    margin: 0 auto;
    margin-top: -6px;
    background: #dde4f6;
}

.fileuploader-theme-thumbnails .fileuploader-items .fileuploader-item.sorting {
    padding-top: 0;
    margin: 0;
}

.fileuploader-theme-thumbnails .fileuploader-sorter-placeholder {
    background: #f0f3f9;
    border-radius: 6px;
}

@media all and (max-width:768px) {
    .fileuploader-theme-thumbnails .fileuploader-thumbnails-input,
    .fileuploader-theme-thumbnails .fileuploader-items-list .fileuploader-item {
        width: 33.33333333%;
        width: calc(33.33333333% - 16px);
        padding-top: 30%;
    }
}

@media all and (max-width:480px) {
    .fileuploader-theme-thumbnails .fileuploader-thumbnails-input,
    .fileuploader-theme-thumbnails .fileuploader-items-list .fileuploader-item {
        width: 50%;
        width: calc(50% - 16px);
        padding-top: 40%;
    }
}

.fileuploader-theme-dropin .fileuploader-input {
    margin: -16px;
    padding: 16px;
    border: 0;
}

.fileuploader-theme-dropin .fileuploader-input.fileuploader-dragging {
    background: #f3f5fa;
    border-radius: 6px;
}

.fileuploader-theme-dropin .fileuploader-input-inner {
    width: 100%;
    text-align: center;
    padding: 16px;
    color: #5b5b7b;
}

.fileuploader-theme-dropin .fileuploader-input-inner span {
    display: inline-block;
    text-decoration: underline;
}

.fileuploader-theme-dropin .fileuploader-input-inner span:hover {
    color: #4a4a64;
}

.fileuploader-theme-boxafter {
    padding-top: 0.1px;
}

.fileuploader-theme-boxafter .fileuploader-input {
    display: block;
    margin-top: 48px;
    padding: 28px 0;
    background: rgba(120, 155, 236, 0.06);
    border: 2px dashed #d5dee8;
    border-radius: 14px;
    text-align: center;
    color: #bbc2d4;
    transition: all 0.2s ease;
}

.fileuploader-theme-boxafter .fileuploader-input h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 400;
}

.fileuploader-theme-boxafter .fileuploader-input h3 a {
    color: #789bec;
    text-decoration: none;
    font-weight: 700;
}

.fileuploader-theme-boxafter .fileuploader-input-button {
    position: absolute;
    top: -24px;
    right: 12px;
    width: 48px;
    height: 48px;
    padding: 0;
    font-size: 22px;
    font-weight: 400;
    line-height: 48px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
}

.fileuploader-theme-boxafter .fileuploader-input.fileuploader-dragging {
    transform: scale(1.03);
    background: rgba(120, 155, 236, 0.1);
}

.fileuploader-theme-boxafter .fileuploader-items {
    margin-top: -1px;
}

.fileuploader-theme-boxafter .fileuploader-items .fileuploader-item .fileuploader-progressbar {
    flex: 0;
    bottom: -6px;
    margin-left: 52px;
    width: calc(100% - 52px);
    background: rgba(0, 0, 0, 0.06);
}

.fileuploader-theme-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    padding: 0;
    margin: 0;
    background: none;
}

.fileuploader-theme-avatar .fileuploader-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.fileuploader-theme-avatar .fileuploader-wrapper,
.fileuploader-theme-avatar .fileuploader-items .fileuploader-item .fileuploader-item-image {
    background: #f9f9fc;
}

.fileuploader-theme-avatar .fileuploader-items {
    height: 100%;
}

.fileuploader-theme-avatar .fileuploader-items .fileuploader-item {
    height: 100%;
    padding: 0;
    margin: 0;
}

.fileuploader-theme-avatar .fileuploader-items .fileuploader-item.is-image-waiting .fileuploader-item-image img {
    display: none;
}

.fileuploader-theme-avatar .fileuploader-droparea {
    position: absolute;
    display: flex;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    background: rgba(43, 56, 71, 0.6);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: -1;
    opacity: 0;
    transform: scale(1.3);
}

.fileuploader-theme-avatar .fileuploader-droparea .fileuploader-icon-main {
    font-size: 32px;
    color: #fff;
}

.fileuploader-theme-avatar .fileuploader-dragging .fileuploader-droparea {
    opacity: 1;
    transform: scale(1);
    z-index: 1;
}

.fileuploader-theme-avatar .progressbar3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fileuploader-theme-avatar .progressbar3 span {
    position: relative;
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    color: #1f344a;
    z-index: 1;
}

.fileuploader-theme-avatar .progressbar3 [data-action] {
    cursor: pointer;
}

.fileuploader-theme-avatar .progressbar3 svg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.fileuploader-theme-avatar .progressbar3 svg circle {
    stroke: #5b7bfe;
    stroke-width: 4px;
    fill: transparent;
    transition: 0.3s stroke-dashoffset, 0.3s transform;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}

.fileuploader-theme-avatar .progressbar3.is-reset svg circle {
    transition: none;
}

.fileuploader-theme-avatar .progressbar3 svg .progress-dash {
    stroke: #dae2fd;
}

.fileuploader-theme-avatar .fileuploader-item.upload-complete .progressbar3 svg {
    transform: scale(0.5);
}

.fileuploader-theme-avatar .fileuploader-item.upload-complete .progressbar3 span {
    font-size: 16px;
    color: #fff;
}

.fileuploader-theme-avatar .fileuploader-item.upload-complete .progressbar3 svg .progress-dash {
    stroke-width: 0;
    transform: scale(1.3);
}

.fileuploader-theme-avatar .fileuploader-item.upload-successful .progressbar3 svg circle {
    stroke: #43D084;
    fill: #43D084;
}

.fileuploader-theme-avatar .fileuploader-item.upload-successful .progressbar3 svg .progress-dash {
    fill: rgba(67, 208, 132, 0.6);
}

.fileuploader-theme-avatar .fileuploader-item.upload-failed .progressbar3 svg circle {
    stroke: #FE7676;
    fill: #FE7676;
}

.fileuploader-theme-avatar .fileuploader-item.upload-failed .progressbar3 svg .progress-dash {
    fill: rgba(254, 118, 118, 0.6);
}

.fileuploader-theme-avatar .fileuploader-menu {
    position: absolute;
    bottom: 6%;
    right: 8%;
    z-index: 1;
}

.fileuploader-theme-avatar .fileuploader-menu-open {
    padding: 2px 4px;
    border-radius: 4px;
    background: #fff;
    color: #5b7bfe;
    font-size: 18px;
    line-height: 0;
    box-shadow: 1px 2px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

.fileuploader-theme-avatar .fileuploader-menu ul {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    list-style: none;
    margin: 0;
    padding: 0;
    margin-top: -1px;
    box-shadow: 1px 6px 10px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 4px 4px;
}

.fileuploader-theme-avatar .fileuploader-menu.is-shown .fileuploader-menu-open {
    transform: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.fileuploader-theme-avatar .fileuploader-menu.is-shown ul {
    display: block;
}

.fileuploader-theme-avatar .fileuploader-menu ul li {
    margin: 0;
    padding: 0;
}

.fileuploader-theme-avatar .fileuploader-menu ul li a {
    display: block;
    padding: 10px 12px;
    min-width: 120px;
    color: #1f344a;
    cursor: pointer;
    text-decoration: none;
}

.fileuploader-theme-avatar .fileuploader-menu ul li a i {
    margin-right: 6px;
}

.fileuploader-theme-avatar .fileuploader-menu ul li a:hover {
    background: rgba(0, 0, 0, 0.03)
}

.fileuploader-theme-avatar .fileuploader-menu ul li a:active {
    background: rgba(0, 0, 0, 0.05)
}

.fileuploader-theme-avatar:hover .fileuploader-menu {
    transform: translateY(0);
    opacity: 1;
}

.fileuploader-popup-preview.is-for-avatar .fileuploader-cropper .fileuploader-cropper-area .area-image,
.fileuploader-popup-preview.is-for-avatar .fileuploader-cropper .fileuploader-cropper-area .area-move:after {
    border-radius: 50%;
}

.fileuploader .is-hidden {
    display: none !important;
}

.fileuploader-theme-gallery .fileuploader-input,
.fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item {
    position: relative;
    display: inline-block;
    margin: 16px 0 0 16px;
    padding: 0;
    vertical-align: top;
    width: 16.66666667%;
    width: calc(16.66666667% - 16px);
    height: 100px;
}

.fileuploader-theme-gallery .fileuploader-input-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 210px;
    border: 2px dashed #d5d9e0;
    text-align: center;
    color: #90a0b1;
    font-weight: 700;
    cursor: pointer;
    border-radius: 8px;
    user-select: none;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    height: 100px!important;
    min-height: 100px!important;
    max-height: 100px!important;
}

.fileuploader-theme-gallery .fileuploader-input-inner .fileuploader-icon-main {
    font-size: 34px;
    margin-bottom: 8px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.fileuploader-theme-gallery .fileuploader-input-inner:hover {
    border-color: #b8beca;
    background: #fcfcfe;
    box-shadow: 0 4px 24px -1px rgba(0, 0, 0, 0.05);
}

.fileuploader-theme-gallery .fileuploader-input-inner:hover .fileuploader-icon-main {
    margin-bottom: 14px;
}

.fileuploader-theme-gallery .fileuploader-input-inner:active,
.fileuploader-theme-gallery .fileuploader-dragging .fileuploader-input-inner {
    background: #fffffe;
}

.fileuploader-theme-gallery .fileuploader-input-inner:active .fileuploader-icon-main,
.fileuploader-theme-gallery .fileuploader-dragging .fileuploader-input-inner .fileuploader-icon-main {
    margin-bottom: 4px;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-items-list {
    display: flex;
    flex-wrap: wrap;
    margin: -16px 0 0 -16px;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item {
    border-bottom: 0;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item:last-child {
    margin-bottom: 0;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item-inner {
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 24px -1px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    z-index: 1;
}

.fileuploader-theme-gallery .fileuploader-item-inner .thumbnail-holder {
    position: relative;
    width: 100%;
    padding-top: 75%;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
    height: 100px;
}

.fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    border-radius: 0;
    overflow: hidden;
}

.fileuploader-theme-gallery .thumbnail-holder .fileuploader-item-icon {
    border-radius: 0;
    font-size: 34px;
    background-size: 30%;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item .fileuploader-action-popup {
    border-radius: 0;
    z-index: 1;
}

.fileuploader-theme-gallery .fileuploader-item .actions-holder {
    position: absolute;
    top: 6px;
    right: 0;
    width: 100%;
    height: 20px;
    padding: 0 6px;
    z-index: 2;
    text-align: right;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item .fileuploader-action {
    color: #fff;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item .fileuploader-action+.fileuploader-action {
    margin-left: 8px;
}

.fileuploader-theme-gallery .fileuploader-item-inner .content-holder {
    padding: 12px 8px;
    text-align: center;
}

.fileuploader-theme-gallery .fileuploader-item-inner .content-holder h5 {
    width: 100%;
    margin: 0;
    color: #35354f;
    font-size: 14px;
    font-weight: 700;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.fileuploader-theme-gallery .fileuploader-item-inner .content-holder span {
    font-size: 12px;
    color: #999a9f;
}

.fileuploader-theme-gallery .fileuploader-item .type-holder {
    position: absolute;
    top: 100%;
    right: -4px;
    width: 32px;
    height: 32px;
    margin-top: -24px;
    text-transform: uppercase;
    z-index: 1;
}

.fileuploader-theme-gallery .fileuploader-item .gallery-item-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 34px;
    margin: 8px 0 0;
    padding: 0;
    background: #fff;
    box-shadow: 1px 4px 12px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    overflow: hidden;
    z-index: 1;
}

.fileuploader-theme-gallery .fileuploader-item .gallery-item-dropdown a {
    display: block;
    padding: 10px 12px;
    min-width: 120px;
    color: #35354f;
    cursor: pointer;
    text-decoration: none;
}

.fileuploader-theme-gallery .fileuploader-item .gallery-item-dropdown a:hover {
    background: rgba(0, 0, 0, 0.03)
}

.fileuploader-theme-gallery .fileuploader-item .gallery-item-dropdown a:active {
    background: rgba(0, 0, 0, 0.05)
}

.fileuploader-theme-gallery .file-main-1 .fileuploader-item-inner {
    background: #789bec;
}

.fileuploader-theme-gallery .file-main-1 .fileuploader-item-inner .content-holder h5 {
    color: #fff;
}

.fileuploader-theme-gallery .file-main-1 .fileuploader-item-inner .content-holder span {
    color: rgba(255, 255, 255, 0.8);
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item.upload-failed .thumbnail-holder {
    background: #db6868;
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item.upload-failed .thumbnail-holder:after {
    content: 'Error!';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    font-weight: 700;
    color: #9e4c4c;
}

.fileuploader-theme-gallery .fileuploader-item .progress-holder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #454545;
    display: none;
    z-index: 1;
}

.fileuploader-theme-gallery .fileuploader-item .progress-holder span {
    position: absolute;
    top: 50%;
    left: 50%;
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    transform: translateX(-50%) translateY(-50%);
    z-index: 1;
}

.fileuploader-theme-gallery .fileuploader-item .progress-holder .fileuploader-progressbar {
    height: 100%;
    border-radius: 0;
}

.fileuploader-theme-gallery .fileuploader-item .progress-holder .fileuploader-progressbar .bar {
    width: 100%;
    height: 0;
    top: auto;
    bottom: 0;
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
}

.fileuploader-theme-gallery .fileuploader-items .fileuploader-item.sorting {
    padding-top: 0;
    margin: 0;
}

.fileuploader-theme-gallery .fileuploader-sorter-placeholder {
    background: #f0f3f9;
    border-radius: 6px;
}

@media all and (max-width:1200px) {
    .fileuploader-theme-gallery .fileuploader-input,
    .fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item {
        width: 25%;
        width: calc(25% - 16px);
    }
    .fileuploader-theme-gallery .fileuploader-input-inner {
        min-height: 220px;
    }
}

@media all and (max-width:768px) {
    .fileuploader-theme-gallery .fileuploader-input,
    .fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item {
        width: 33.33333333%;
        width: calc(33.33333333% - 16px);
    }
    .fileuploader-theme-gallery .fileuploader-input-inner {
        min-height: 225px;
    }
}

@media all and (max-width:576px) {
    .fileuploader-theme-gallery .fileuploader-input,
    .fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item {
        width: 50%;
        width: calc(50% - 16px);
    }
    .fileuploader-theme-gallery .fileuploader-input-inner {
        min-height: 182px;
    }
}

body {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    background: #f9f9fc;
    color: #5b5b7b;
}

h3.h2 {
    font-weight: bold;
    margin-top: 0;
}

a {
    color: #5b7bfe;
}

a:hover,
a:active {
    text-decoration: underline;
    color: #5b7bfe;
}

p {
    color: #979fb8;
    line-height: 24px;
}

.container {
    padding-left: 28px;
    padding-right: 28px;
}

code {
    padding: 2px 4px;
    background: #ebeef1;
    color: #74809d;
    border-radius: 4px;
}

body pre.prettyprint {
    position: relative;
    background: #101118 !important;
    color: #b1b1b4;
    padding: 18px;
    padding-top: 42px;
    border: 0;
    border-radius: 9px;
    box-shadow: 0 6px 10px;
    word-break: break-word;
}

body pre.prettyprint:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 24px;
    width: 100%;
    background: #2b2b2b url(../images/code_window.png) no-repeat center left;
}

body pre.prettyprint code {
    padding: 0;
    border-radius: 0;
    background: transparent;
    white-space: pre-wrap;
    word-break: break-word;
}

.material-icons {
    line-height: normal;
    font-size: inherit;
    vertical-align: middle;
}

.btn {
    padding: 8px 16px;
    border-radius: 34px;
}

.check {
    display: none;
}

.check+label {
    cursor: pointer;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    transform: translate3d(0, 0, 0);
}

.check+label svg {
    position: relative;
    z-index: 1;
    fill: none;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke: #ccc;
    stroke-width: 1;
    transform: translate3d(0, 0, 0);
    transition: all 0.2s ease;
    vertical-align: middle;
    margin-right: 6px;
    margin-top: -3px;
}

.check+label svg path {
    stroke-dasharray: 60;
    stroke-dashoffset: 0;
}

.check+label svg polyline {
    stroke-dasharray: 22;
    stroke-dashoffset: 66;
}

.check+label:hover svg {
    stroke: #37d83a;
}

.check:checked+label svg {
    stroke: #37d83a;
}

.check:checked+label svg path {
    stroke-dashoffset: 60;
    transition: all 0.1s linear;
}

.check:checked+label svg polyline {
    stroke-dashoffset: 42;
    transition: all 0.1s linear;
    transition-delay: 0.1s;
}

.button {
    display: inline-block;
    margin: 0;
    padding: 12px 20px;
    border: none;
    border-radius: 34px;
    outline: 0;
    font-weight: 700;
    cursor: pointer;
    vertical-align: middle;
    text-decoration: none;
    background: #9658fe;
    background: -moz-linear-gradient(-45deg, #3a8ffe 0, #9658fe 100%);
    background: -webkit-linear-gradient(-45deg, #3a8ffe 0, #9658fe 100%);
    background: linear-gradient(135deg, #3a8ffe 0, #9658fe 100%);
    background-size: 140% auto;
    background-position: center;
    color: #fff;
    -webkit-transition: all .1s ease;
    transition: all .1s ease;
}

.button:hover,
.button:focus {
    color: #fff;
    background-position: left;
    text-decoration: none;
}

.button:active {
    color: #fff;
    background-position: right;
    text-decoration: none;
}

.text-muted {
    color: #979fb8 !important;
}

.alert {
    padding: 20px 26px;
    border: 0;
    border-radius: 6px;
    font-weight: 700;
}

.alert-success {
    background: #ebf9ea;
    color: #4bcb4a;
}

.notification {
    padding: 12px 16px;
    background: #ea4c89;
    text-align: center;
    color: #fff;
}

.notification a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: underline;
}

.notification a:hover {
    color: #fff;
}

.notification img {
    max-height: 20px;
    margin-right: 8px;
}

.header {
    position: relative;
    z-index: 2;
}

.header .logo h1 {
    margin: 0;
    padding: 32px;
    padding-left: 28px;
    background: url('../images/fileuploader2x.png') no-repeat left;
    color: #5b7bfe;
    font-size: 18px;
    font-weight: bold;
}

.header .logo h1 a {
    font-size: 9px;
}

.header .menu {
    margin: 0;
}

.header .menu li a {
    display: inline-block;
    padding: 32px 4px;
    color: #5b7bfe;
    font-weight: bold;
}

.header .menu li a:hover,
.header .menu li a:focus,
.header .menu li a:active {
    text-decoration: underline;
}

.header.header-alt .menu li a {
    color: #fff;
}

.header-message {
    padding: 14px;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    background: #00d664;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.header-message p {
    margin: 0;
    color: #fff;
}

.header-message .button {
    margin-left: 10px;
    padding: 8px 16px;
    background: #fff;
    color: #00d664;
    border-radius: 30px;
}


@media all and (min-width:768px) {
    .fileuploader-theme-gallery .fileuploader-input-inner {
        min-height: 195px;
    }
    .fileuploader-theme-gallery .fileuploader-input,
    .fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item {
        width: 33.33333333%;
        width: calc(33.33333333% - 16px);
    }
}

@media all and (max-width:992px) {
    .main-content .promo-button {
        display: none;
    }
}

@media all and (max-width:768px) {
    .header.header-alt .menu li a {
        color: #5b7bfe;
    }
    .fileuploader-theme-gallery .fileuploader-input-inner {
        min-height: 150px;
    }
    .main-circle {
        display: none;
    }
}

@media all and (max-width:360px) {
    .fileuploader-theme-gallery .fileuploader-input-inner {
        min-height: 150px;
    }

    .fileuploader-theme-gallery .fileuploader-input,
    .fileuploader-theme-gallery .fileuploader-items-list .fileuploader-item {
        width: 100%;
        width: calc(100% - 16px);
    }
}
.fileuploader-action:hover {
    background: #843534!important;
    line-height: 17px!important;;
}
.fileuploader-action i {
    font-size: 8pt!important;
}

.fileuploader-action i.fa-check:hover:before {
    content: "\f00d";
}
