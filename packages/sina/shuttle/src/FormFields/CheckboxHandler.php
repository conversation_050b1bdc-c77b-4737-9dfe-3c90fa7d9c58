<?php

namespace Sina\Shuttle\FormFields;

class CheckboxHandler extends <PERSON>bstract<PERSON>and<PERSON>
{
    protected $codename = 'checkbox';

    public function createContent($row, $dataType, $dataTypeContent, $options)
    {
        return view('shuttle::formfields.checkbox', [
            'row'             => $row,
            'options'         => $options,
            'dataType'        => $dataType,
            'dataTypeContent' => $dataTypeContent,
        ]);
    }
}
