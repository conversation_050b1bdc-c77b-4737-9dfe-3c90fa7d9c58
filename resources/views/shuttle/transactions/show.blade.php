<div class="row">
    <div class="col-12">
        <div class="row">
            <div class="col-md-6">
        <div class="section-title">
            <h1>Transaction Info</h1>
        </div>
        <div class="list">
            <ul>
                <li><strong>Name:</strong> {{$transaction->name}}</li>
                <li><strong>Company:</strong> {{$transaction->company}}</li>
                <li><strong>Job:</strong> {{$transaction->job}}</li>
                <li><strong>Mobile:</strong> {{$transaction->mobile}}</li>
                <li><strong>Address1:</strong> {{$transaction->address1}}</li>
                <li><strong>Address2:</strong> {{$transaction->address2}}</li>
                <li><strong>City:</strong> {{$transaction->city}}</li>
                <li><strong>ZipCode:</strong> {{$transaction->zip_code}}</li>
                <li><strong>Country:</strong> {{$transaction->country}}</li>
                <li><strong>Price:</strong> £{{$transaction->price}}</li>
                <li><strong>Payment method:</strong> {{$transaction->payment_method ?? 'online'}}</li>
                <li><strong>Date:</strong> {{$transaction->created_at}}</li>
            </ul>
        </div>
        <!-- /.list -->
            </div>
            <!-- /.col-md-6 -->
      <div class="col-md-6">
        <div class="section-title">
            <h1>User Info</h1>
        </div>
        <div class="userinfo">
            <div class="name">
                <figure>
                    <img src="{{Storage::url($transaction->user->image)}}" />
                </figure>
                <div class="infos">
                    <h2>{{$transaction->user->fullName}}</h2>
                    <span>{{$transaction->user->mobile}}</span>
                </div>
                <!-- /.infos -->


            </div>
            <!-- /.name -->

            <ul>
                <li><strong>Email:</strong> {{$transaction->user->email}}</li>
{{--                <li><strong>Mobile:</strong>  {{$transaction->user->mobile}}</li>--}}
                <li><strong>ID number:</strong>  {{$transaction->user->id_number}}</li>
                <li><strong>Registred at:</strong>  {{$transaction->created_at}}</li>
            </ul>
        </div>
        <!-- /.userinfo -->
      </div>
      </div>
            <!-- /.row -->
    </div>


    <div class="col-md-12">
        <div class="row">
            @if($myCourse->count())
                <div class="col-md-12 mt-5">
                    <div class="section-title">
                        <h1>Courses</h1>
                    </div>
                </div>
                @foreach($myCourse as $c)
                    <div class="col-md-6">
                        <div class="l">
                            <div class="l-item">
                                <div class="l-item__title">
                                    <span>{{$c->model->course->category->name}}</span>
                                    <h2><a href="https://business-eagles.shuttle.ge/courses/1">{{$c->model->course->title}}</a></h2>
                                    <ul>
                                        <li>
                                            <figure>
                                                <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M15 3C16.95 8.84 16.95 15.16 15 21" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                </svg>
                                            </figure>
                                            <div class="title-init">
                                                <p>Location</p>
                                                <span>{{$c->model->location->name}}</span>
                                            </div>
                                        </li>
                                        <li>
                                            <figure>
                                                <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"><path d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z"></path></svg>
                                            </figure>
                                            <div class="title-init">
                                                <p>Duration</p>
                                                <span>{{$c->model->duration}} common.day</span>
                                            </div>
                                        </li>
                                        <li>
                                            <figure>
                                                <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g><path id="Vector" d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></g>
                                                </svg>
                                            </figure>
                                            <div class="title-init">
                                                <p>Upcoming Date</p>
                                                <span>{{$c->model->upComingFormat}}</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                                <div class="l-item__img">
                                    <figure><img src="{{Storage::url($c->model->course->cover)}}" alt="" /></figure>
                                    <div class="price">£{{$c->model->price}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif

            @if($myConference->count())
                <div class="col-md-12 mt-5">
                    <div class="section-title">
                        <h1>Conferences</h1>
                    </div>
                </div>


                @foreach($myConference as $item)
                    <div class="col-md-6">
                        <div class="l l-c">
                            <div class="l-item">
                                <div class="l-item__title">
                                    <span>{{$item->model->conference->category?->name}}</span>
                                    <h2><a href="{{route('conference.show', Str::slug($item->model->conference->title, '-'))}}">{{$item->model->conference->title}}</a></h2>
                                    <ul>
                                        <li>
                                            <figure>
                                                <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M15 3C16.95 8.84 16.95 15.16 15 21" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                </svg>
                                            </figure>
                                            <div class="title-init">
                                                <p>Location</p>
                                                <span>{{$item->model->conference->location->name}}</span>
                                            </div>
                                        </li>
                                        <li>
                                            <figure>
                                                <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"><path d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z"></path></svg>
                                            </figure>
                                            <div class="title-init">
                                                <p>Duration</p>
                                                <span>{{$item->model->duration}} common.day</span>
                                            </div>
                                        </li>
                                        <li>
                                            <figure>
                                                <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g id="Calendar / Calendar">
                                                        <path id="Vector" d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </g>
                                                </svg> </figure>
                                            <div class="title-init">
                                                <p>Upcoming Date</p>
                                                <span>{{$item->model->upComingFormat}}</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                                <div class="l-item__img">
                                    <figure><img src="{{Storage::url($item->model->conference->cover)}}" alt=""></figure>
                                    <div class="price">£{{$item->model->price}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</div>