ace.define("ace/mode/alda_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"], function(require, exports, module) {
    "use strict";
    
    var oop = require("../lib/oop");
    var TextHighlightRules = require("./text_highlight_rules").TextHighlightRules;
    
    var AldaHighlightRules = function() {
    
        this.$rules = {
            pitch: [{
                token: "variable.parameter.operator.pitch.alda",
                regex: /(?:[+\-]+|\=)/
            }, {
                token: "",
                regex: "",
                next: "timing"
            }],
            timing: [{
                token: "string.quoted.operator.timing.alda",
                regex: /\d+(?:s|ms)?/
            }, {
                token: "",
                regex: "",
                next: "start"
            }],
            start: [{
                token: [
                    "constant.language.instrument.alda",
                    "constant.language.instrument.alda",
                    "meta.part.call.alda",
                    "storage.type.nickname.alda",
                    "meta.part.call.alda"
                ],
                regex: /^([a-zA-Z]{2}[\w\-+\'()]*)((?:\s*\/\s*[a-zA-Z]{2}[\w\-+\'()]*)*)(?:(\s*)(\"[a-zA-Z]{2}[\w\-+\'()]*\"))?(\s*:)/
            }, {
                token: [
                    "text",
                    "entity.other.inherited-class.voice.alda",
                    "text"
                ],
                regex: /^(\s*)(V\d+)(:)/
            }, {
                token: "comment.line.number-sign.alda",
                regex: /#.*$/
            }, {
                token: "entity.name.function.pipe.measure.alda",
                regex: /\|/
            }, {
                token: "comment.block.inline.alda",
                regex: /\(comment\b/,
                push: [{
                    token: "comment.block.inline.alda",
                    regex: /\)/,
                    next: "pop"
                }, {
                    defaultToken: "comment.block.inline.alda"
                }]
            }, {
                token: "entity.name.function.marker.alda",
                regex: /%[a-zA-Z]{2}[\w\-+\'()]*/
            }, {
                token: "entity.name.function.at-marker.alda",
                regex: /@[a-zA-Z]{2}[\w\-+\'()]*/
            }, {
                token: "keyword.operator.octave-change.alda",
                regex: /\bo\d+\b/
            }, {
                token: "keyword.operator.octave-shift.alda",
                regex: /[><]/
            }, {
                token: "keyword.operator.repeat.alda",
                regex: /\*\s*\d+/
            }, {
                token: "string.quoted.operator.timing.alda",
                regex: /[.]|r\d*(?:s|ms)?/
            },{
                token: "text",
                regex: /([cdefgab])/,
                next: "pitch"
            }, {
                token: "string.quoted.operator.timing.alda",
                regex: /~/,
                next: "timing"
            }, {
                token: "punctuation.section.embedded.cram.alda",
                regex: /\}/,
                next: "timing"
            }, {
                token: "constant.numeric.subchord.alda",
                regex: /\//
            }, {
                todo: {
                    token: "punctuation.section.embedded.cram.alda",
                    regex: /\{/,
                    push: [{
                        token: "punctuation.section.embedded.cram.alda",
                        regex: /\}/,
                        next: "pop"
                    }, {
                        include: "$self"
                    }]
                }
            }, {
                todo: {
                    token: "keyword.control.sequence.alda",
                    regex: /\[/,
                    push: [{
                        token: "keyword.control.sequence.alda",
                        regex: /\]/,
                        next: "pop"
                    }, {
                        include: "$self"
                    }]
                }
            }, {
                token: "meta.inline.clojure.alda",
                regex: /\(/,
                push: [{
                    token: "meta.inline.clojure.alda",
                    regex: /\)/,
                    next: "pop"
                }, {
                    include: "source.clojure"
                }, {
                    defaultToken: "meta.inline.clojure.alda"
                }]
            }]
        };
        
        this.normalizeRules();
    };
    
    AldaHighlightRules.metaData = {
        scopeName: "source.alda",
        fileTypes: ["alda"],
        name: "Alda"
    };
    
    
    oop.inherits(AldaHighlightRules, TextHighlightRules);
    
    exports.AldaHighlightRules = AldaHighlightRules;
    });

ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"], function(require, exports, module) {
"use strict";

var oop = require("../../lib/oop");
var Range = require("../../range").Range;
var BaseFoldMode = require("./fold_mode").FoldMode;

var FoldMode = exports.FoldMode = function(commentRegex) {
    if (commentRegex) {
        this.foldingStartMarker = new RegExp(
            this.foldingStartMarker.source.replace(/\|[^|]*?$/, "|" + commentRegex.start)
        );
        this.foldingStopMarker = new RegExp(
            this.foldingStopMarker.source.replace(/\|[^|]*?$/, "|" + commentRegex.end)
        );
    }
};
oop.inherits(FoldMode, BaseFoldMode);

(function() {
    
    this.foldingStartMarker = /([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/;
    this.foldingStopMarker = /^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/;
    this.singleLineBlockCommentRe= /^\s*(\/\*).*\*\/\s*$/;
    this.tripleStarBlockCommentRe = /^\s*(\/\*\*\*).*\*\/\s*$/;
    this.startRegionRe = /^\s*(\/\*|\/\/)#?region\b/;
    this._getFoldWidgetBase = this.getFoldWidget;
    this.getFoldWidget = function(session, foldStyle, row) {
        var line = session.getLine(row);
    
        if (this.singleLineBlockCommentRe.test(line)) {
            if (!this.startRegionRe.test(line) && !this.tripleStarBlockCommentRe.test(line))
                return "";
        }
    
        var fw = this._getFoldWidgetBase(session, foldStyle, row);
    
        if (!fw && this.startRegionRe.test(line))
            return "start"; // lineCommentRegionStart
    
        return fw;
    };

    this.getFoldWidgetRange = function(session, foldStyle, row, forceMultiline) {
        var line = session.getLine(row);
        
        if (this.startRegionRe.test(line))
            return this.getCommentRegionBlock(session, line, row);
        
        var match = line.match(this.foldingStartMarker);
        if (match) {
            var i = match.index;

            if (match[1])
                return this.openingBracketBlock(session, match[1], row, i);
                
            var range = session.getCommentFoldRange(row, i + match[0].length, 1);
            
            if (range && !range.isMultiLine()) {
                if (forceMultiline) {
                    range = this.getSectionRange(session, row);
                } else if (foldStyle != "all")
                    range = null;
            }
            
            return range;
        }

        if (foldStyle === "markbegin")
            return;

        var match = line.match(this.foldingStopMarker);
        if (match) {
            var i = match.index + match[0].length;

            if (match[1])
                return this.closingBracketBlock(session, match[1], row, i);

            return session.getCommentFoldRange(row, i, -1);
        }
    };
    
    this.getSectionRange = function(session, row) {
        var line = session.getLine(row);
        var startIndent = line.search(/\S/);
        var startRow = row;
        var startColumn = line.length;
        row = row + 1;
        var endRow = row;
        var maxRow = session.getLength();
        while (++row < maxRow) {
            line = session.getLine(row);
            var indent = line.search(/\S/);
            if (indent === -1)
                continue;
            if  (startIndent > indent)
                break;
            var subRange = this.getFoldWidgetRange(session, "all", row);
            
            if (subRange) {
                if (subRange.start.row <= startRow) {
                    break;
                } else if (subRange.isMultiLine()) {
                    row = subRange.end.row;
                } else if (startIndent == indent) {
                    break;
                }
            }
            endRow = row;
        }
        
        return new Range(startRow, startColumn, endRow, session.getLine(endRow).length);
    };
    this.getCommentRegionBlock = function(session, line, row) {
        var startColumn = line.search(/\s*$/);
        var maxRow = session.getLength();
        var startRow = row;
        
        var re = /^\s*(?:\/\*|\/\/|--)#?(end)?region\b/;
        var depth = 1;
        while (++row < maxRow) {
            line = session.getLine(row);
            var m = re.exec(line);
            if (!m) continue;
            if (m[1]) depth--;
            else depth++;

            if (!depth) break;
        }

        var endRow = row;
        if (endRow > startRow) {
            return new Range(startRow, startColumn, endRow, line.length);
        }
    };

}).call(FoldMode.prototype);

});

ace.define("ace/mode/alda",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/alda_highlight_rules","ace/mode/folding/cstyle"], function(require, exports, module) {
"use strict";

var oop = require("../lib/oop");
var TextMode = require("./text").Mode;
var AldaHighlightRules = require("./alda_highlight_rules").AldaHighlightRules;
var FoldMode = require("./folding/cstyle").FoldMode;

var Mode = function() {
    this.HighlightRules = AldaHighlightRules;
    this.foldingRules = new FoldMode();
};
oop.inherits(Mode, TextMode);

(function() {
    this.$id = "ace/mode/alda";
}).call(Mode.prototype);

exports.Mode = Mode;
});                (function() {
                    ace.require(["ace/mode/alda"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            