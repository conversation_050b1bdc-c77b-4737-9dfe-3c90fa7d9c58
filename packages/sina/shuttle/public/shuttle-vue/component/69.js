"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[69],{69:(t,e,n)=>{n.r(e),n.d(e,{default:()=>i});const o={components:{editor:n(3251)},data:function(){return{content:""}},mounted:function(){var t=document.getElementById("html");this.content=t.value,t.remove()},methods:{editorInit:function(){n(5335),n(1258),n(4579),n(3763),n(2298),n(8602)},format:function(t){var e="",n="";return t.split(/>\s*</).forEach((function(t){t.match(/^\/\w/)&&(n=n.substring("\t".length)),e+=n+"<"+t+">\r\n",t.match(/^<?\w[^>]*[^\/]$/)&&!t.startsWith("input")&&(n+="\t")})),e.substring(1,e.length-3)}}};const i=(0,n(1900).Z)(o,(function(){var t=this,e=t._self._c;return e("div",[e("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.content,expression:"content"}],attrs:{name:"html",hidden:""},domProps:{value:t.content},on:{input:function(e){e.target.composing||(t.content=e.target.value)}}}),t._v(" "),t._t("default")],2),t._v(" "),e("editor",{attrs:{id:"editor",lang:"html",theme:"chrome",width:"100%",height:"700"},on:{init:t.editorInit},model:{value:t.content,callback:function(e){t.content=e},expression:"content"}})],1)}),[],!1,null,null,null).exports}}]);