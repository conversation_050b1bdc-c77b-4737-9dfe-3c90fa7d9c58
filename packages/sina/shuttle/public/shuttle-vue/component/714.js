"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[714],{3714:(t,e,n)=>{n.r(e),n.d(e,{default:()=>s});const a={components:{Switches:n(6880).Z},props:{originalTable:{type:Object,required:!1,default:function(){return{}}},types:{type:Object,default:function(){return{}}}},data:function(){return{table:{name:"",columns:[]},tableJson:""}},mounted:function(){this.table=this.originalTable},methods:{addColumn:function(t){this.table.columns.push(t)},makeColumn:function(t){return $.extend({name:"",oldName:"",type:{name:"bigint",category:"Numbers",default:{type:"number",step:"any"}},length:null,fixed:!1,unsigned:!1,autoincrement:!1,notnull:!1,default:null},t)},addNewColumn:function(){this.addColumn(this.makeColumn())},addTimestamps:function(){this.addColumn(this.makeColumn({name:"created_at",type:{name:"timestamp"}})),this.addColumn(this.makeColumn({name:"updated_at",type:{name:"timestamp"}}))},addSoftDeletes:function(){this.addColumn(this.makeColumn({name:"deleted_at",type:{name:"timestamp"}}))},deleteColumn:function(t){this.table.columns.splice(t,1)},addTranslations:function(){this.addColumn(this.makeColumn({name:"locale",type:{name:"varchar"},length:4})),this.addColumn(this.makeColumn({name:this.table.name.replace("_translations","_id"),type:{name:"integer"}}))},saveTable:function(){var t=this;this.tableJson=JSON.stringify(this.table),this.$nextTick((function(){return t.$emit("submit")}))}}};const s=(0,n(1900).Z)(a,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"row"},[e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.tableJson,expression:"tableJson"}],attrs:{hidden:"",name:"table"},domProps:{value:t.tableJson},on:{input:function(e){e.target.composing||(t.tableJson=e.target.value)}}}),t._v(" "),e("div",{staticClass:"col-md-6"},[e("label",{attrs:{for:"name"}},[t._v("Table name")]),e("br"),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model.trim",value:t.table.name,expression:"table.name",modifiers:{trim:!0}}],staticClass:"form-control",attrs:{type:"text",required:""},domProps:{value:t.table.name},on:{input:function(e){e.target.composing||t.$set(t.table,"name",e.target.value.trim())},blur:function(e){return t.$forceUpdate()}}})]),t._v(" "),e("div",{staticClass:"col-md-2"},[e("label",{attrs:{for:"name"}},[t._v("Table name")]),e("br"),t._v(" "),e("switches")],1),t._v(" "),e("div",{staticClass:"col-md-2"},[e("label",{attrs:{for:"name"}},[t._v("Table name")]),e("br"),t._v(" "),e("switches")],1),t._v(" "),e("div",{staticClass:"col-md-2 text-right"},[e("br"),t._v(" "),e("button",{staticClass:"btn btn-primary",attrs:{type:"button"},on:{click:t.saveTable}},[t._v("\n      Save\n    ")])]),t._v(" "),e("div",{staticClass:"col-12 mt-3"},[e("table",{staticClass:"table table-bordered"},[t._m(0),t._v(" "),e("tbody",t._l(t.table.columns,(function(n,a){return e("database-table-row",{key:a,on:{columnDeleted:function(e){return t.deleteColumn(a)}},model:{value:t.table.columns[a],callback:function(e){t.$set(t.table.columns,a,e)},expression:"table.columns[index]"}})})),1)])]),t._v(" "),e("div",{staticClass:"col-12 text-center"},[e("button",{staticClass:"btn btn-success",attrs:{type:"button"},on:{click:t.addNewColumn}},[t._v("\n      + New Column\n    ")]),t._v(" "),e("button",{staticClass:"btn btn-success",attrs:{type:"button"},on:{click:t.addTimestamps}},[t._v("\n      + Add Timestamps\n    ")]),t._v(" "),e("button",{staticClass:"btn btn-success",attrs:{type:"button"},on:{click:t.addSoftDeletes}},[t._v("\n      + Add Soft Deletes\n    ")]),t._v(" "),e("button",{staticClass:"btn btn-success",attrs:{type:"button"},on:{click:t.addTranslations}},[t._v("\n      + Add Translations\n    ")])])])}),[function(){var t=this,e=t._self._c;return e("thead",{staticClass:"thead-light"},[e("tr",[e("th",[t._v("Name")]),t._v(" "),e("th",[t._v("Type")]),t._v(" "),e("th",[t._v("Length")]),t._v(" "),e("th",[t._v("Default")]),t._v(" "),e("th",[t._v("Unsigned")]),t._v(" "),e("th",[t._v("Required")]),t._v(" "),e("th",[t._v("A_I")]),t._v(" "),e("th")])])}],!1,null,null,null).exports}}]);