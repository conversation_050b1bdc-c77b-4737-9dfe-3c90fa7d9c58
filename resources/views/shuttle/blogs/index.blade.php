@extends('shuttle::admin')

@section('breadcrumbs')
    @include('shuttle::includes.breadcrumbs', ['breadCrumbs' => ['Home' => route('shuttle.index'),
    $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)], 'btn' =>
    ($add) ? route('shuttle.scaffold_interface.create',$scaffoldInterface) : null])
@stop

@push('css-vendors2')
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/dataTables.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/datatables.responsive.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="https://cdn.datatables.net/searchpanes/2.0.2/css/searchPanes.dataTables.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/select/1.4.0/css/select.dataTables.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.3/css/buttons.dataTables.min.css" />
@endpush

@section('main')

<div class="row mb-5">
    <div class="col-md-12">
        <div class="card">
            <form class="row" method="GET">
                <div class="col-md-6">
                    <div class="form-group">
                        <input name="q" type="text" class="form-control" placeholder="Search by title..." value="{{ request('q') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <input name="start_date" type="date" class="form-control" placeholder="Start Date" value="{{ request('start_date') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <input name="end_date" type="date" class="form-control" placeholder="End Date" value="{{ request('end_date') }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <button class="btn btn-primary" type="submit">Search</button>
                        <a href="{{ route('shuttle.scaffold_interface.index', $scaffoldInterface) }}" class="btn btn-secondary">Clear</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<scaffold-interface-filter-modal></scaffold-interface-filter-modal>

<scaffold-interface-table
    url="{{ route('shuttle.scaffold_interface.datatable', array_merge(['scaffold_interface' => $scaffoldInterface], request()->all())) }}"
    delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
    :columns="{{ json_encode($columns) }}"
>
</scaffold-interface-table>

@stop


