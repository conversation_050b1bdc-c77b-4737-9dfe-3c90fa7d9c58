<?php

namespace Sina\Shuttle\Http\Controllers;

use Sina\Shuttle\Models\Component;
use App\Http\Controllers\Controller;
use Sina\Shuttle\Models\Page;
use Sina\Shuttle\Models\PageComponent;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Models\Section;
use Sina\Shuttle\Models\ShortCode;
use Sina\Shuttle\Models\Type;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class SectionController extends BaseController
{
    public function store(Type $type, Request $request)
    {
        $type->sections()->create([]);
        return redirect()->back()->withErrors([
            'success' => ['Section added']
        ]);
    }

    public function update(Section $section,Request $request)
    {

        $scaffold = ScaffoldInterface::where('model',$section->type->model)->with('rows')->first();
        $pages = $section->type->pages;
        $position = $request->position ?? 0;

        $sourceFile = resource_path("views/sections/");

        try {
            if(!File::isDirectory($sourceFile)){
                if (!File::makeDirectory($sourceFile, 0755, true)) {
                    throw new \Exception("Cannot create directory: " . $sourceFile);
                }
            }

            if($position == 0){
                foreach ($pages as $page) {
                    foreach (config('laravellocalization.supportedLocales') as $lang => $val){
                        $langDir = $sourceFile.$lang.'/';
                        if(!File::isDirectory($langDir)){
                            if (!File::makeDirectory($langDir, 0755, true)) {
                                throw new \Exception("Cannot create directory: " . $langDir);
                            }
                        }
                        $contents = (new ShortCode)->parserCode($request->body, $scaffold, $request->model, [], $page->components()->wherePivot('locale',$lang)->get());
                        if (!File::put($langDir.$page->id.".blade.php",$contents)) {
                            throw new \Exception("Cannot write to file: " . $langDir.$page->id.".blade.php");
                        }
                    }
                }
            }else{
                $contents = (new ShortCode)->parserCode($request->body, $scaffold, $request->model, [], []);
                if (!File::put($sourceFile.$section->id.".blade.php",$contents)) {
                    throw new \Exception("Cannot write to file: " . $sourceFile.$section->id.".blade.php");
                }
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Section update file operation failed: ' . $e->getMessage(), [
                'section_id' => $section->id,
                'position' => $position,
                'source_file' => $sourceFile,
                'user_id' => auth()->id()
            ]);

            return redirect()->back()->withErrors([
                'warning' => ['Section data was updated successfully, but template files could not be updated due to file permissions. Please contact your system administrator.']
            ]);
        }

        $section->update($request->all());

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function userUpdate(Section $section,Request $request)
    {
        $lang =config('translatable.locales')[0];
        if($request->has('lang')){
            $lang = $request->lang;
        }

        $data = $request->except('translate');
        $data[$lang] = $request->translate;

        $section->update($data);

        return redirect()->back();
    }



    public function componentAdd(Request $request)
    {
        $lang = $request->get('lang',config('translatable.locales')[0]);
        $page = Page::where('id',$request->page_id)->with('type')->first();
        $component = Component::find($request->component_id);

        // Check if page and page type exist
        if (!$page || !$page->type) {
            return redirect()->back()->withErrors([
                'error' => ['Page or page type not found']
            ]);
        }

        $scaffold = ScaffoldInterface::where('model', $page->type->model)->with('rows')->first();
        $page->components()->attach([$component->id => ['setting' => [], 'locale' => $lang, 'model_type' => Page::class]]);
        $sourceFile = resource_path("views/sections/".$lang.'/');

        try {
            if(!File::isDirectory($sourceFile)){
                if (!File::makeDirectory($sourceFile, 0755, true)) {
                    throw new \Exception("Cannot create directory: " . $sourceFile);
                }
            }

            $contents = (new ShortCode)->parserCode(optional(optional($page->type->sections)->first())->body, $scaffold, $request->model, [], $page->components()->where('locale',$lang)->get());

            if (!File::put($sourceFile.$page->id.".blade.php",$contents)) {
                throw new \Exception("Cannot write to file: " . $sourceFile.$page->id.".blade.php");
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Component add file operation failed: ' . $e->getMessage(), [
                'page_id' => $request->page_id,
                'component_id' => $request->component_id,
                'lang' => $lang,
                'source_file' => $sourceFile,
                'user_id' => auth()->id()
            ]);

            if($this->is_api) {
                return response()->json([
                    'error' => 'File operation failed. Component was added but template could not be updated.',
                    'components' => $page->components
                ], 500);
            }

            return redirect()->back()->withErrors([
                'warning' => ['Component was added successfully, but template file could not be updated due to file permissions. Please contact your system administrator.']
            ]);
        }

        if($this->is_api)
        {
            return ['components' => $page->components];
        }

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function sort(Request $request)
    {
//        $data = json_decode($request->data,true);

        $i = 1;
        foreach ($request->data ?? [] as $item) {
            $menuItem = PageComponent::find($item);
            if (!empty($menuItem)) {
                $menuItem->position = $i++;
                $menuItem->save();
            }
        }

        return $request->data;
    }

    public function componentRemove(PageComponent $pageComponent)
    {
        $lang = $pageComponent->locale;
        $page = Page::where('id',$pageComponent->page->id)->with('type')->first();

        // Check if page and page type exist
        if (!$page || !$page->type) {
            return redirect()->back()->withErrors([
                'error' => ['Page or page type not found']
            ]);
        }

        $scaffold = ScaffoldInterface::where('model', $page->type->model)->with('rows')->first();
        $pageComponent->delete();
        $sourceFile = resource_path("views/sections/".$lang.'/');

        try {
            if(!File::isDirectory($sourceFile)){
                if (!File::makeDirectory($sourceFile, 0755, true)) {
                    throw new \Exception("Cannot create directory: " . $sourceFile);
                }
            }

            $contents = (new ShortCode)->parserCode(optional(optional($page->type->sections)->first())->body, $scaffold, $page->type->model, [], $page->components()->where('locale',$lang)->get());

            if (!File::put($sourceFile.$page->id.".blade.php",$contents)) {
                throw new \Exception("Cannot write to file: " . $sourceFile.$page->id.".blade.php");
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Component remove file operation failed: ' . $e->getMessage(), [
                'page_id' => $page->id,
                'component_id' => $pageComponent->component_id,
                'lang' => $lang,
                'source_file' => $sourceFile,
                'user_id' => auth()->id()
            ]);

            if($this->is_api) {
                return response()->json([
                    'error' => 'File operation failed. Component was removed but template could not be updated.',
                    'components' => $page->components()->orderBy('position')->wherePivot('locale',$lang)->get()
                ], 500);
            }

            return redirect()->back()->withErrors([
                'warning' => ['Component was removed successfully, but template file could not be updated due to file permissions. Please contact your system administrator.']
            ]);
        }

        if($this->is_api)
        {
            return $page->components()->orderBy('position')->wherePivot('locale',$lang)->get();
        }

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

}
