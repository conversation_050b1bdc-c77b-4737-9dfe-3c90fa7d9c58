<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Models\ScaffoldInterface;
use Illuminate\Http\Request;
use App\Models\Blog;
use Sina\Shuttle\Http\Resources\DataTableResource;

class BlogsController extends ShuttleController
{
    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface, function ($query) use ($request) {
                    // Apply search if provided
                    if (!empty($request->q)) {
                        $query->where('title', 'LIKE', "%{$request->q}%");
                    }

                    // Apply date filters
                    if (!empty($request->start_date)) {
                        $query->whereDate('created_at', '>=', $request->start_date);
                    }

                    if (!empty($request->end_date)) {
                        $query->whereDate('created_at', '<=', $request->end_date);
                    }

                    return $query;
                })
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }
}
