<?php

namespace App\Models;

class Awward extends \Illuminate\Database\Eloquent\Model
{
	use \Illuminate\Database\Eloquent\Factories\HasFactory;

	protected $guarded = ['id'];

public function components()
	{
		return $this->belongsToMany('Sina\Shuttle\Models\Component','shuttle_page_component', 'model_id')->using('Sina\Shuttle\Models\PageComponent')->withPivot('setting','component_id','locale','id')->wherePivot('model_type', static::class)->orderBy('position');
	}


	public function lecturers()
	{
		return $this->belongsToMany("App\Models\Lecturer", "awwards_lecturers", "conference_id", "lecturer_id");
	}


	public function packages()
	{
		return $this->hasMany("App\Models\AwwardPackage");
	}


	public function upComing()
	{
		return $this->hasOne("App\Models\AwwardPackage", 'awward_id')->where('start_at', '>', now())->whereNotNull('start_at')->whereNotNull('end_at')->orderBy('start_at', 'asc');
	}


	public function location()
	{
		return $this->belongsTo("App\Models\Location");
	}


	public function category()
	{
		return $this->belongsTo("App\Models\AwwardCategory", 'category_id');
	}


	public function form()
	{
		return $this->belongsTo("App\Models\Form");
	}
}
