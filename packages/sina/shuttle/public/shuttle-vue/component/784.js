"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[784],{1784:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const n={props:{inputs:{type:Array,default:[]},value:{type:Object,default:{}},prefix:{type:String,default:""}},methods:{componentName:function(e){var t="".concat(_.camelCase(e.type),"Input");return t[0].toUpperCase()+t.slice(1)},inputName:function(e){return this.prefix?this.prefix+"["+e.field+"]":e.field}}};const i=(0,a(1900).Z)(n,(function(){var e=this,t=e._self._c;return t("div",e._l(e.inputs,(function(a){return t("div",{key:"array-item-".concat(a.field,"-").concat(a.id),staticClass:"form-group"},[t("label",[e._v(e._s(a.display_name))]),e._v(" "),"image"==a.type?t("image-input",{attrs:{name:e.inputName(a),path:e.value[a.field],preview:e.value[a.field]?e.value[a.field].replace("public","/storage"):""}}):t(e.componentName(a),{tag:"component",attrs:{name:e.inputName(a),value:e.value[a.field]}})],1)})),0)}),[],!1,null,null,null).exports}}]);