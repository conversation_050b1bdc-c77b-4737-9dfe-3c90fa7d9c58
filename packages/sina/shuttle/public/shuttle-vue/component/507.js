"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[507],{7244:(t,e,n)=>{n.d(e,{Z:()=>i});var a=n(3645),o=n.n(a)()((function(t){return t[1]}));o.push([t.id,".ti-tag-input[data-v-230b2aac]{background-color:transparent;border:none;color:inherit;display:flex;line-height:inherit;margin:0;padding:0;position:absolute;top:0;width:100%}.ti-tag-input[data-v-230b2aac]::-ms-clear{display:none}input[data-v-230b2aac]:focus{outline:none}input[disabled][data-v-230b2aac]{background-color:transparent}",""]);const i=o},2507:(t,e,n)=>{n.r(e),n.d(e,{default:()=>c});const a={props:{scope:{type:Object},name:{type:String,default:""}}};var o=n(3379),i=n.n(o),s=n(7244),r={insert:"head",singleton:!1};i()(s.Z,r);s.Z.locals;const c=(0,n(1900).Z)(a,(function(){var t=this,e=t._self._c;return t.scope.edit?e("input",{directives:[{name:"model",rawName:"v-model",value:t.scope.tag.text,expression:"scope.tag.text"}],staticClass:"ti-tag-input",attrs:{maxlength:t.scope.maxlength,type:"text",size:"1",name:t.name},domProps:{value:t.scope.tag.text},on:{input:[function(e){e.target.composing||t.$set(t.scope.tag,"text",e.target.value)},function(e){return t.scope.validateTag(t.scope.index,e)}],blur:function(e){return t.scope.performCancelEdit(t.scope.index)},keydown:function(e){return t.scope.performSaveEdit(t.scope.index,e)}}}):t._e()}),[],!1,null,"230b2aac",null).exports}}]);