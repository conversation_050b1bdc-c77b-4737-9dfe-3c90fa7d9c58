"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[635],{5635:(t,a,e)=>{e.r(a),e.d(a,{default:()=>n});const i={props:{modalId:{type:String,required:!0},isFade:{type:Boolean,required:!1,default:!0},parentClass:{type:String,required:!1,default:""},isShown:{type:Boolean,default:function(){return!1}},preventDefault:{type:Boolean,default:function(){return!1}},isSingleParam:{type:Boolean,default:function(){return!1}},centered:{type:Boolean,default:function(){return!1}},size:{type:String,default:function(){return"xl"}}},data:function(){return{isShowModal:!1}},computed:{modalSize:function(){return"md"==this.size?"":"modal-"+this.size}},watch:{isShown:function(){this.isShown&&this.preventDefault?this.openModal():this.closeModal()}},mounted:function(){var t=this;t.hashCheckForEditing(),$(window).on("hashchange",(function(){t.hashCheckForEditing()}))},methods:{hashCheckForEditing:function(){this.isShowModal=!1;var t=caHash.get();if(_.startsWith(t,"#"+this.modalId)){var a=this.isSingleParam?caHash.getParamSingleAndClear(t):caHash.getParamsMultipleAndClear(t);this.isSingleParam&&a=="#"+this.modalId&&(a=null),this.$emit("onHashParams",a),this.preventDefault||this.openModal()}},openModal:function(){var t=this;this.$nextTick((function(){t.isShowModal=!0,$("#"+t.modalId).modal("show"),_.delay((function(){validationBootstrap.init()}))}))},closeModal:function(){$("#"+this.modalId).modal("hide"),this.isShowModal=!1}}};const n=(0,e(1900).Z)(i,(function(){var t=this,a=t._self._c;return a("div",{class:["modal fade",t.isFade?"fade":"",t.parentClass?t.parentClass:""],attrs:{id:t.modalId,tabindex:"-1",role:"dialog"}},[a("div",{staticClass:"modal-dialog",class:[{"modal-dialog-centered":t.centered},t.modalSize],attrs:{role:"document"}},[t.isShowModal?a("div",{staticClass:"modal-content"},[t._t("default")],2):t._e()])])}),[],!1,null,null,null).exports}}]);