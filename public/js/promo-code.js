/**
 * Promo Code Functionality
 */

class PromoCodeManager {
    constructor() {
        this.appliedPromoCode = null;
        this.originalAmount = 0;
        this.discountAmount = 0;
        this.finalAmount = 0;
        
        this.init();
    }

    init() {
        // Initialize promo code toggle
        const promoToggle = document.getElementById('promocode');
        const promoInput = document.getElementById('promo-input');
        
        if (promoToggle && promoInput) {
            promoToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    promoInput.classList.remove('d-none');
                } else {
                    promoInput.classList.add('d-none');
                    this.clearPromoCode();
                }
            });
        }

        // Initialize promo code button click (no form now, just div + button)
        const promoSubmitBtn = document.querySelector('#promo-submit-btn');
        if (promoSubmitBtn) {
            console.log('Adding click event listener to promo submit button:', promoSubmitBtn);
            promoSubmitBtn.addEventListener('click', (e) => {
                console.log('Promo submit button clicked');
                e.preventDefault();
                e.stopPropagation();
                this.validatePromoCode();
                return false;
            });
        } else {
            console.log('Promo submit button not found');
        }

        // Also handle Enter key in input field
        const promoInputField = document.querySelector('#promo-input input[name="promo_code"]');
        if (promoInputField) {
            console.log('Adding keypress event listener to promo input field:', promoInputField);
            promoInputField.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('Enter key pressed in promo input');
                    e.preventDefault();
                    this.validatePromoCode();
                }
            });
        }

        // Initialize promo code input in forms
        this.initPromoCodeInputs();
    }

    initPromoCodeInputs() {
        const promoInputs = document.querySelectorAll('input[name="promo_code"], input[name="promoocode"]');
        
        promoInputs.forEach(input => {
            input.addEventListener('blur', () => {
                if (input.value.trim()) {
                    this.validatePromoCodeInline(input);
                }
            });
        });
    }

    async validatePromoCode() {
        const promoInput = document.querySelector('#promo-input input[name="promo_code"]');
        const submitBtn = document.querySelector('#promo-submit-btn');

        console.log('Promo validation started', { promoInput, submitBtn });

        if (!promoInput || !promoInput.value.trim()) {
            this.showMessage('Please enter a promo code.', 'error');
            return;
        }

        const code = promoInput.value.trim().toUpperCase();
        const amount = this.getOrderAmount();

        console.log('Promo code validation', { code, amount });

        if (amount <= 0) {
            this.showMessage('Unable to calculate order amount. Amount: ' + amount, 'error');
            return;
        }

        // Show loading state
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Validating...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/api/promo-code/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ code, amount })
            });

            const data = await response.json();

            if (data.valid) {
                this.applyPromoCode(data);
                this.showMessage(data.message, 'success');
            } else {
                this.showMessage(data.message, 'error');
            }
        } catch (error) {
            console.error('Promo code validation error:', error);
            this.showMessage('Error validating promo code. Please try again.', 'error');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    async validatePromoCodeInline(input) {
        const code = input.value.trim().toUpperCase();
        if (!code) return;

        const amount = this.getOrderAmount();
        if (amount <= 0) return;

        try {
            const response = await fetch('/api/promo-code/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ code, amount })
            });

            const data = await response.json();

            // Add visual feedback to input
            input.classList.remove('is-valid', 'is-invalid');
            
            if (data.valid) {
                input.classList.add('is-valid');
                this.showInlineMessage(input, `Valid! ${data.discount_percentage}% discount`, 'success');
            } else {
                input.classList.add('is-invalid');
                this.showInlineMessage(input, data.message, 'error');
            }
        } catch (error) {
            console.error('Inline promo code validation error:', error);
        }
    }

    applyPromoCode(data) {
        this.appliedPromoCode = {
            code: data.code,
            discountPercentage: data.discount_percentage,
            discountAmount: parseFloat(data.discount_amount),
            originalAmount: parseFloat(data.original_amount),
            finalAmount: parseFloat(data.final_amount)
        };

        // Update UI to show applied discount
        this.updatePriceDisplay();
        
        // Hide promo input and show applied code
        const promoInput = document.getElementById('promo-input');
        if (promoInput) {
            promoInput.style.display = 'none';
        }
        
        this.showAppliedPromoCode();
    }

    clearPromoCode() {
        this.appliedPromoCode = null;
        this.updatePriceDisplay();
        this.hideAppliedPromoCode();
    }

    updatePriceDisplay() {
        // Update price displays in the UI
        const priceElements = document.querySelectorAll('.total-price, .final-amount');
        
        priceElements.forEach(element => {
            if (this.appliedPromoCode) {
                element.textContent = `$${this.appliedPromoCode.finalAmount}`;
            } else {
                element.textContent = `$${this.originalAmount}`;
            }
        });

        // Show discount breakdown if applied
        if (this.appliedPromoCode) {
            this.showDiscountBreakdown();
        } else {
            this.hideDiscountBreakdown();
        }
    }

    showAppliedPromoCode() {
        let appliedDiv = document.getElementById('applied-promo-code');
        
        if (!appliedDiv) {
            appliedDiv = document.createElement('div');
            appliedDiv.id = 'applied-promo-code';
            appliedDiv.className = 'applied-promo-code alert alert-success';
            
            const promoSection = document.querySelector('.promo');
            if (promoSection) {
                promoSection.appendChild(appliedDiv);
            }
        }

        appliedDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>
                    <strong>${this.appliedPromoCode.code}</strong> applied 
                    (${this.appliedPromoCode.discountPercentage}% off)
                </span>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="promoCodeManager.clearPromoCode()">
                    Remove
                </button>
            </div>
        `;
        appliedDiv.style.display = 'block';
    }

    hideAppliedPromoCode() {
        const appliedDiv = document.getElementById('applied-promo-code');
        if (appliedDiv) {
            appliedDiv.style.display = 'none';
        }
    }

    showDiscountBreakdown() {
        let breakdownDiv = document.getElementById('discount-breakdown');
        
        if (!breakdownDiv) {
            breakdownDiv = document.createElement('div');
            breakdownDiv.id = 'discount-breakdown';
            breakdownDiv.className = 'discount-breakdown mt-2';
            
            const priceSection = document.querySelector('.promo') || document.querySelector('.total-section');
            if (priceSection) {
                priceSection.appendChild(breakdownDiv);
            }
        }

        breakdownDiv.innerHTML = `
            <div class="price-breakdown">
                <div class="d-flex justify-content-between">
                    <span>Subtotal:</span>
                    <span>$${this.appliedPromoCode.originalAmount}</span>
                </div>
                <div class="d-flex justify-content-between text-success">
                    <span>Discount (${this.appliedPromoCode.discountPercentage}%):</span>
                    <span>-$${this.appliedPromoCode.discountAmount}</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between font-weight-bold">
                    <span>Total:</span>
                    <span>$${this.appliedPromoCode.finalAmount}</span>
                </div>
            </div>
        `;
        breakdownDiv.style.display = 'block';
    }

    hideDiscountBreakdown() {
        const breakdownDiv = document.getElementById('discount-breakdown');
        if (breakdownDiv) {
            breakdownDiv.style.display = 'none';
        }
    }

    getOrderAmount() {
        // Try to get amount from various sources
        const priceElement = document.querySelector('.total-price, .final-amount, [data-price]');

        console.log('Getting order amount from element:', priceElement);

        if (priceElement) {
            // First try data-price attribute
            const dataPrice = priceElement.getAttribute('data-price');
            console.log('Data price attribute:', dataPrice);

            if (dataPrice) {
                const amount = parseFloat(dataPrice);
                console.log('Parsed data price:', amount);
                return isNaN(amount) ? 0 : amount;
            }

            // Fallback to text content
            const priceText = priceElement.textContent || '0';
            console.log('Price text content:', priceText);

            const amount = parseFloat(priceText.replace(/[^0-9.]/g, ''));
            console.log('Parsed text amount:', amount);
            return isNaN(amount) ? 0 : amount;
        }

        console.log('No price element found, returning 0');
        return 0;
    }

    showMessage(message, type = 'info') {
        // Create or update message display
        let messageDiv = document.getElementById('promo-message');
        
        if (!messageDiv) {
            messageDiv = document.createElement('div');
            messageDiv.id = 'promo-message';
            
            const promoSection = document.querySelector('.promo');
            if (promoSection) {
                promoSection.appendChild(messageDiv);
            }
        }

        messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type} mt-2`;
        messageDiv.textContent = message;
        messageDiv.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }

    showInlineMessage(input, message, type) {
        // Remove existing feedback
        const existingFeedback = input.parentNode.querySelector('.promo-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // Add new feedback
        const feedback = document.createElement('div');
        feedback.className = `promo-feedback small mt-1 text-${type === 'error' ? 'danger' : 'success'}`;
        feedback.textContent = message;
        
        input.parentNode.appendChild(feedback);

        // Auto-hide after 3 seconds
        setTimeout(() => {
            feedback.remove();
        }, 3000);
    }

    // Get applied promo code for form submission
    getAppliedPromoCode() {
        return this.appliedPromoCode;
    }
}

// Initialize promo code manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.promoCodeManager = new PromoCodeManager();
});
