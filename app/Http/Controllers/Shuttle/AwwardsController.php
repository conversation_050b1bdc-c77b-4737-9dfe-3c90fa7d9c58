<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Models\Component;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Models\PageComponent;
use App\Models\Awward;
use Illuminate\Http\Request;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;

class AwwardsController extends ShuttleController
{
    public function addComponent(Request $request, Awward $awward)
    {
        $lang = 'en';
        $component = Component::find($request->component_id);

        // Create PageComponent directly
        \Sina\Shuttle\Models\PageComponent::create([
            'component_id' => $component->id,
            'model_id' => $awward->id,
            'model_type' => Awward::class,
            'setting' => [],
            'locale' => $lang
        ]);

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function copy(Request $request, Awward $awward)
    {

    }

    public function destroyComponent(Request $request, PageComponent $component)
    {
        $component->delete();

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function store(ScaffoldInterface $scaffold_interface, Request $request)
    {
        // dd($scaffold_interface);
        $model = $this->save($scaffold_interface, $request);

        foreach ($request->locations ?? [] as $loc){
            $model->packages()->create($loc);
        }

        return redirect()->route("shuttle.scaffold_interface.index", $scaffold_interface)->with([
            'message' => __('voyager::generic.successfully_updated'),
            'alert-type' => 'success',
        ]);
    }

    public function update(ScaffoldInterface $scaffold_interface, Request $request, $id)
    {
        $model = $this->save($scaffold_interface, $request, $id);

        $ids = [];
        foreach ($request->locations ?? [] as $loc){
            $m = $model->packages()->updateOrCreate(['id' => data_get($loc, 'id')], $loc);
            $ids[] = $m->id;
        }
        $model->packages()->whereNotIn('id', $ids)->delete();

        return redirect()->route("shuttle.scaffold_interface.index", $scaffold_interface)->with([
            'message' => __('voyager::generic.successfully_updated'),
            'alert-type' => 'success',
        ]);
    }

    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface, function ($query) use ($request){
                    if(!empty($request->q)){
                        $query = $query->where('title', 'like', '%'.$request->q.'%');
                    }

                    if(!empty($request->category_id)){
                        $query = $query->where('category_id', $request->category_id);
                    }

                    if(!empty($request->start_at)){
                        $query = $query->whereIn('id', function ($q) use ($request) {
                            $q->select('awwards_id')->from('awwards_packages')->where('start_at', '>=', $request->start_at);
                        });
                    }

                    if(!empty($request->end_at)){
                        $query = $query->whereIn('id', function ($q) use ($request) {
                            $q->select('awwards_id')->from('awwards_packages')->where('end_at', '<=', $request->end_at);
                        });
                    }

                    return $query;
                })
//                ->addAction(fn ($data) => '<a href="' . route('shuttle.conference.copy',  $data->id) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }

}
