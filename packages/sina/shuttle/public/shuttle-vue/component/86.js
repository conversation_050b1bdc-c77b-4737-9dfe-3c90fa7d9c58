"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[86],{1086:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=function(e){return!e.some((function(e){if("number"==typeof e){var t=isFinite(e)&&Math.floor(e)===e;return t||console.warn("Only numerics are allowed for this prop. Found:",e),!t}if("string"==typeof e){var r=/\W|[a-z]|!\d/i.test(e);return r||console.warn("Only alpha strings are allowed for this prop. Found:",e),!r}return console.warn("Only numeric and string values are allowed. Found:",e),!1}))};const a={value:{type:[Array,String],default:"",required:!0},autocompleteItems:{type:Array,default:function(){return[]},validator:function(e){return!e.some((function(e){var t=!e.text;t&&console.warn('Missing property "text"',e);var r=!1;return e.classes&&(r="string"!=typeof e.classes),r&&console.warn('Property "classes" must be type of string',e),t||r}))}},allowEditTags:{type:Boolean,default:!1},autocompleteFilterDuplicates:{default:!0,type:Boolean},addOnlyFromAutocomplete:{type:Boolean,default:!1},autocompleteMinLength:{type:Number,default:1},autocompleteAlwaysOpen:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"Add Tag"},addOnKey:{type:Array,default:function(){return[13]},validator:n},saveOnKey:{type:Array,default:function(){return[13]},validator:n},maxTags:{type:Number},maxlength:{type:Number},validation:{type:Array,default:function(){return[]},validator:function(e){return!e.some((function(e){var t=!e.rule;t&&console.warn('Property "rule" is missing',e);var r=e.rule&&("string"==typeof e.rule||e.rule instanceof RegExp||"[object Function]"==={}.toString.call(e.rule));r||console.warn("A rule must be type of string, RegExp or function. Found:",JSON.stringify(e.rule));var n=!e.classes;n&&console.warn('Property "classes" is missing',e);var a=e.type&&"string"!=typeof e.type;return a&&console.warn('Property "type" must be type of string. Found:',e),!r||t||n||a}))}},separators:{type:Array,default:function(){return[";"]},validator:function(e){return!e.some((function(e){var t="string"!=typeof e;return t&&console.warn("Separators must be type of string. Found:",e),t}))}},avoidAddingDuplicates:{type:Boolean,default:!0},addOnBlur:{type:Boolean,default:!0},isDuplicate:{type:Function,default:null},addFromPaste:{type:Boolean,default:!0},deleteOnBackspace:{default:!0,type:Boolean},name:{default:"",type:String}}}}]);