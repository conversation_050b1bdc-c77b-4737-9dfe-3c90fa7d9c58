.dataTables_wrapper {
    overflow: hidden; }
  
  table.dataTable td {
    padding-top: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f3f3f3;
    outline: initial !important; }
  
  table.dataTable tr:last-of-type td {
    border-bottom: initial; }
  
  table.dataTable {
    width: 100% !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important; }
  
  table p,
  table h6 {
    margin-bottom: initial; }
  
  table.dataTable thead > tr > th.sorting_asc,
  table.dataTable thead > tr > th.sorting_desc,
  table.dataTable thead > tr > th.sorting,
  table.dataTable thead > tr > td.sorting_asc,
  table.dataTable thead > tr > td.sorting_desc,
  table.dataTable thead > tr > td.sorting {
    padding-top: 10px;
    padding-bottom: 10px; }
  
  table.dataTable thead .sorting:after,
  table.dataTable thead .sorting_asc:after,
  table.dataTable thead .sorting_desc:after,
  table.dataTable thead .sorting_asc_disabled:after,
  table.dataTable thead .sorting_desc_disabled:after {
    right: 1.5em; }
  
  table.dataTable thead .sorting:before,
  table.dataTable thead .sorting_asc:before,
  table.dataTable thead .sorting_desc:before,
  table.dataTable thead .sorting_asc_disabled:before,
  table.dataTable thead .sorting_desc_disabled:before {
    right: 2em; }
  
  .dataTables_wrapper .paginate_button.previous {
    margin-right: 15px; }
  
  .dataTables_wrapper .paginate_button.next {
    margin-left: 15px; }
  
  div.dataTables_wrapper div.dataTables_paginate {
    margin-top: 25px; }
  
  div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    justify-content: center; }
  
  .dataTables_wrapper .paginate_button.page-item {
    padding-left: 10px;
    padding-right: 10px; }
  
  table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > td:first-child:before,
  table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > th:first-child:before {
    top: unset;
    box-shadow: initial;
    background-color: #145388;
    font-size: 12px; }
  
    div.dataTables_wrapper div.dataTables_filter input {
        margin-left: 0;
      }