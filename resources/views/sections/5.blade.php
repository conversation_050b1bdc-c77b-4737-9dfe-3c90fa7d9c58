<div class="section blog-details-section section-padding"><div class="container"><div class="blog-details-wrap"><div class="row"><div class="col-lg-8"><div class="blog-details-post"><div class="single-blog-post single-blog"><h3 class="title-blog">{{data_get($data,"title")}}</h3><span class="mb-5 float-left">{{data_get($data,"created_at")->locale(LaravelLocalization::getCurrentLocale())->isoFormat("DD MMMM YYYY")}}</span><div class="blog-image"><a href="blog-details.html"><img src="{{Storage::url(data_get($data,"image"))}}" alt="{{data_get($data,"title")}}"></a></div><div class="blog-content"><div id="context">{!! cleanHtml(data_get($data,"text")) !!}</div> </div></div></div></div> <div class="col-lg-4"><div class="blog-sidebar"><div class="sidebar-widget sidebar-widget-1"><form class="search-form" action="#"><input type="text" placeholder="@lang('common.search_word')" class="highlight"><button type="submit"><i class="icofont-search"></i></button></form></div><div class="sidebar-widget"><div class="widget-title"><h3 class="title">@lang("common.related")</h3></div><div class="recent-posts"><ul>@foreach(\App\Models\Blog::where('id', '<>', $data->id)->limit(3)->get() as $r)<li><a class="post-link" href="{{$r->id}}"><div class="post-thumb"><img src="{{Storage::url($r->image)}}" alt="{{$r->title}}"></div><div class="post-text"><h4 class="title">{{$r->title}}</h4><span class="post-meta"><i class="icofont-calendar"></i> {{$r->created_at->locale(LaravelLocalization::getCurrentLocale())->isoFormat("D MMM YYYY")}}</span></div></a></li>@endforeach</ul></div></div></div></div></div></div></div></div><section class="section partners"><div class="container"><div class="row"><div class="col-md-12"><div class="section-title"><span>Embark on a journey of growth and learning: explore our array of upcoming programs</span><h1>Upcoming Training Courses</h1><ul><li class="s-p"><a href="#"><i class="icofont-rounded-left"></i></a></li><li class="s-n"><a href="#"><i class="icofont-rounded-right"></i></a></li></ul></div></div><div class="col-md-12"><div class="partners-slider"><div class="courses-slider swiper-container"><div class="swiper-wrapper">@foreach(\App\Models\CourseLocation::oldest('start_at')->hasCourse()->limit(10)->get() as $item)<div class="swiper-slide">@include('includes.course-item')</div>@endforeach</div><div class="swiper-pag swiper-pagination1"></div></div></div></div></div></div></section>